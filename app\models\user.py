from tortoise import fields, models
from tortoise.contrib.pydantic import pydantic_model_creator
from passlib.context import CryptContext
from datetime import datetime, timedelta, timezone
import secrets
import hashlib
from typing import Optional, List

# 密码加密上下文
pwd_context = CryptContext(schemes=["bcrypt"], deprecated="auto")


class Role(models.Model):
    """角色表"""

    id = fields.IntField(pk=True)
    name = fields.CharField(
        max_length=50, unique=True, description="角色名称"
    )  # admin/user/guest
    display_name = fields.CharField(max_length=100, description="显示名称")
    description = fields.TextField(null=True, description="角色描述")
    permissions = fields.JSONField(default=list, description="权限列表")
    is_active = fields.BooleanField(default=True, description="是否启用")
    created_at = fields.DatetimeField(auto_now_add=True, description="创建时间")
    updated_at = fields.DatetimeField(auto_now=True, description="更新时间")

    class Meta:
        table = "roles"

    def __str__(self):
        return f"{self.display_name} ({self.name})"


class User(models.Model):
    """用户表"""

    id = fields.IntField(pk=True)
    username = fields.CharField(max_length=50, unique=True, description="用户名")
    email = fields.CharField(max_length=100, unique=True, description="邮箱地址")
    password_hash = fields.CharField(max_length=255, description="密码哈希")
    nickname = fields.CharField(max_length=100, null=True, description="昵称")
    avatar = fields.CharField(max_length=512, null=True, description="头像URL")

    # 用户状态
    status = fields.CharField(
        max_length=20,
        default="pending",
        description="用户状态: pending/active/frozen/deleted",
    )

    # 角色关联
    role: fields.ForeignKeyRelation["Role"] = fields.ForeignKeyField(
        "models.Role",
        related_name="users",
        default=2,  # 默认普通用户角色ID
        description="用户角色",
    )

    # 邮箱验证
    email_verified = fields.BooleanField(default=False, description="邮箱是否已验证")
    email_verify_token = fields.CharField(
        max_length=255, null=True, description="邮箱验证令牌"
    )
    email_verify_expires = fields.DatetimeField(
        null=True, description="邮箱验证令牌过期时间"
    )

    # 密码重置
    password_reset_token = fields.CharField(
        max_length=255, null=True, description="密码重置令牌"
    )
    password_reset_expires = fields.DatetimeField(
        null=True, description="密码重置令牌过期时间"
    )

    # 登录相关
    last_login_at = fields.DatetimeField(null=True, description="最后登录时间")
    login_attempts = fields.IntField(default=0, description="登录失败次数")
    locked_until = fields.DatetimeField(null=True, description="账户锁定到期时间")

    # 积分系统
    points = fields.IntField(default=10, description="用户积分")

    # 个人信息管理相关字段
    last_nickname_change = fields.DatetimeField(
        null=True, description="上次昵称修改时间"
    )
    nickname_change_count = fields.IntField(default=0, description="昵称修改次数")

    # 时间戳
    created_at = fields.DatetimeField(auto_now_add=True, description="创建时间")
    updated_at = fields.DatetimeField(auto_now=True, description="更新时间")

    class Meta:
        table = "users"

    def __str__(self):
        return f"{self.username} ({self.email})"

    @classmethod
    def hash_password(cls, password: str) -> str:
        """密码哈希"""
        return pwd_context.hash(password)

    def verify_password(self, password: str) -> bool:
        """验证密码"""
        return pwd_context.verify(password, self.password_hash)

    def set_password(self, password: str):
        """设置密码"""
        self.password_hash = self.hash_password(password)

    def generate_email_verify_token(self) -> str:
        """生成邮箱验证令牌"""
        token = secrets.token_urlsafe(32)
        self.email_verify_token = hashlib.sha256(token.encode()).hexdigest()
        self.email_verify_expires = datetime.now(timezone.utc) + timedelta(hours=24)
        return token

    def generate_password_reset_token(self) -> str:
        """生成密码重置令牌"""
        token = secrets.token_urlsafe(32)
        self.password_reset_token = hashlib.sha256(token.encode()).hexdigest()
        self.password_reset_expires = datetime.now(timezone.utc) + timedelta(hours=24)
        return token

    def verify_email_token(self, token: str) -> bool:
        """验证邮箱验证令牌"""
        if not self.email_verify_token or not self.email_verify_expires:
            return False
        current_time = datetime.now(timezone.utc)
        # 确保比较的两个datetime对象都有时区信息
        expires_time = self.email_verify_expires
        if expires_time.tzinfo is None:
            expires_time = expires_time.replace(tzinfo=timezone.utc)
        if current_time > expires_time:
            return False
        token_hash = hashlib.sha256(token.encode()).hexdigest()
        return token_hash == self.email_verify_token

    def verify_password_reset_token(self, token: str) -> bool:
        """验证密码重置令牌"""
        if not self.password_reset_token or not self.password_reset_expires:
            return False
        current_time = datetime.now(timezone.utc)
        # 确保比较的两个datetime对象都有时区信息
        expires_time = self.password_reset_expires
        if expires_time.tzinfo is None:
            expires_time = expires_time.replace(tzinfo=timezone.utc)
        if current_time > expires_time:
            return False
        token_hash = hashlib.sha256(token.encode()).hexdigest()
        return token_hash == self.password_reset_token

    def is_locked(self) -> bool:
        """检查账户是否被锁定"""
        if not self.locked_until:
            return False
        current_time = datetime.now(timezone.utc)
        locked_until = self.locked_until
        if locked_until.tzinfo is None:
            locked_until = locked_until.replace(tzinfo=timezone.utc)
        return current_time < locked_until

    def lock_account(self, duration_minutes: int = 30):
        """锁定账户"""
        self.locked_until = datetime.now(timezone.utc) + timedelta(
            minutes=duration_minutes
        )

    def unlock_account(self):
        """解锁账户"""
        self.locked_until = None
        self.login_attempts = 0

    async def get_permissions(self) -> List[str]:
        """获取用户权限列表"""
        await self.fetch_related("role")
        return self.role.permissions if self.role else []

    def can_change_nickname(self) -> bool:
        """检查是否可以修改昵称（每年只能修改一次）"""
        if not self.last_nickname_change:
            return True

        # 计算距离上次修改的时间
        current_time = datetime.now(timezone.utc)
        last_change = self.last_nickname_change
        if last_change.tzinfo is None:
            last_change = last_change.replace(tzinfo=timezone.utc)
        time_since_last_change = current_time - last_change
        return time_since_last_change.days >= 365

    def get_next_nickname_change_date(self) -> Optional[datetime]:
        """获取下次可以修改昵称的时间"""
        if not self.last_nickname_change:
            return None
        return self.last_nickname_change + timedelta(days=365)

    async def get_current_avatar_url(self) -> Optional[str]:
        """获取当前头像URL"""
        avatar = await UserAvatar.filter(user=self, is_active=True).first()
        if avatar:
            return avatar.cdn_url or avatar.file_path
        return None

    async def deactivate_all_avatars(self):
        """停用所有头像"""
        await UserAvatar.filter(user=self).update(is_active=False)

    async def cleanup_old_avatars(self, keep_recent: int = 1):
        """
        清理旧头像文件

        Args:
            keep_recent: 保留最近几个头像文件作为备份，默认保留1个
        """
        from app.services.avatar_service import AvatarService
        import logging

        logger = logging.getLogger(__name__)

        try:
            # 获取所有非活跃的头像，按创建时间倒序
            old_avatars = (
                await UserAvatar.filter(user=self, is_active=False)
                .order_by("-created_at")
                .offset(keep_recent)
            )

            deleted_count = 0
            for avatar in old_avatars:
                try:
                    # 删除存储文件
                    await AvatarService._delete_file(
                        avatar.file_path, avatar.storage_type
                    )

                    # 删除数据库记录
                    await avatar.delete()
                    deleted_count += 1

                    logger.info(f"清理旧头像: {avatar.file_path}")

                except Exception as e:
                    logger.error(f"清理头像失败 {avatar.file_path}: {e}")
                    # 继续处理其他文件，不因单个文件失败而中断
                    continue

            if deleted_count > 0:
                logger.info(f"用户 {self.username} 清理了 {deleted_count} 个旧头像文件")

        except Exception as e:
            logger.error(f"清理旧头像异常: {e}")
            # 清理失败不应该影响主流程

    async def has_permission(self, permission: str) -> bool:
        """检查用户是否有指定权限"""
        permissions = await self.get_permissions()
        return permission in permissions

    async def add_points(
        self,
        amount: int,
        transaction_type: str,
        description: str,
        related_id: Optional[int] = None,
    ) -> "PointsTransaction":
        """增加用户积分"""
        self.points += amount
        await self.save()

        # 创建积分变动记录
        transaction = await PointsTransaction.create(
            user=self,
            amount=amount,
            balance_after=self.points,
            transaction_type=transaction_type,
            description=description,
            related_id=related_id,
        )
        return transaction

    async def deduct_points(
        self,
        amount: int,
        transaction_type: str,
        description: str,
        related_id: Optional[int] = None,
    ) -> "PointsTransaction":
        """扣除用户积分"""
        if self.points < amount:
            raise ValueError(f"积分不足，当前积分：{self.points}，需要扣除：{amount}")

        self.points -= amount
        await self.save()

        # 创建积分变动记录
        transaction = await PointsTransaction.create(
            user=self,
            amount=-amount,
            balance_after=self.points,
            transaction_type=transaction_type,
            description=description,
            related_id=related_id,
        )
        return transaction


class UserSession(models.Model):
    """用户会话表"""

    id = fields.IntField(pk=True)
    user: fields.ForeignKeyRelation[User] = fields.ForeignKeyField(
        "models.User",
        related_name="sessions",
        on_delete=fields.CASCADE,
        description="关联用户",
    )
    session_token = fields.CharField(
        max_length=255, unique=True, description="会话令牌"
    )
    refresh_token = fields.CharField(
        max_length=255, unique=True, description="刷新令牌"
    )
    expires_at = fields.DatetimeField(description="过期时间")
    user_agent = fields.TextField(null=True, description="用户代理")
    ip_address = fields.CharField(
        max_length=45, null=True, description="IP地址"
    )  # 支持IPv6
    is_active = fields.BooleanField(default=True, description="是否活跃")
    created_at = fields.DatetimeField(auto_now_add=True, description="创建时间")

    class Meta:
        table = "user_sessions"

    def __str__(self):
        return f"Session {self.id} for {self.user.username}"

    def is_expired(self) -> bool:
        """检查会话是否过期"""
        current_time = datetime.now(timezone.utc)
        expires_at = self.expires_at
        if expires_at.tzinfo is None:
            expires_at = expires_at.replace(tzinfo=timezone.utc)
        return current_time > expires_at


class PointsTransaction(models.Model):
    """积分变动记录表"""

    id = fields.IntField(pk=True)
    user: fields.ForeignKeyRelation[User] = fields.ForeignKeyField(
        "models.User",
        related_name="points_transactions",
        on_delete=fields.CASCADE,
        description="关联用户",
    )
    amount = fields.IntField(description="积分变动数量（正数为增加，负数为减少）")
    balance_after = fields.IntField(description="变动后的积分余额")
    transaction_type = fields.CharField(
        max_length=50,
        description="交易类型：register/help_request/help_answer/answer_accepted/admin_adjust",
    )
    description = fields.CharField(max_length=255, description="变动描述")
    related_id = fields.IntField(
        null=True, description="关联的业务ID（如求助ID、回答ID等）"
    )
    created_at = fields.DatetimeField(auto_now_add=True, description="创建时间")

    class Meta:
        table = "points_transactions"
        ordering = ["-created_at"]

    def __str__(self):
        return f"用户 {self.user.username} {'+' if self.amount > 0 else ''}{self.amount} 积分"


class UserNicknameHistory(models.Model):
    """用户昵称修改历史表"""

    id = fields.IntField(pk=True)
    user: fields.ForeignKeyRelation[User] = fields.ForeignKeyField(
        "models.User",
        related_name="nickname_history",
        on_delete=fields.CASCADE,
        description="关联用户",
    )
    old_nickname = fields.CharField(max_length=100, null=True, description="旧昵称")
    new_nickname = fields.CharField(max_length=100, description="新昵称")
    change_reason = fields.CharField(max_length=255, null=True, description="修改原因")
    ip_address = fields.CharField(
        max_length=45, null=True, description="修改时的IP地址"
    )
    user_agent = fields.CharField(max_length=512, null=True, description="用户代理")
    created_at = fields.DatetimeField(auto_now_add=True, description="修改时间")

    class Meta:
        table = "user_nickname_history"
        ordering = ["-created_at"]

    def __str__(self):
        return f"用户 {self.user.username} 昵称修改: {self.old_nickname} -> {self.new_nickname}"


class UserAvatar(models.Model):
    """用户头像存储表"""

    id = fields.IntField(pk=True)
    user: fields.ForeignKeyRelation[User] = fields.ForeignKeyField(
        "models.User",
        related_name="avatars",
        on_delete=fields.CASCADE,
        description="关联用户",
    )
    original_filename = fields.CharField(max_length=255, description="原始文件名")
    file_path = fields.CharField(max_length=512, description="文件存储路径")
    file_size = fields.IntField(description="文件大小（字节）")
    mime_type = fields.CharField(max_length=100, description="MIME类型")
    width = fields.IntField(null=True, description="图片宽度")
    height = fields.IntField(null=True, description="图片高度")
    is_active = fields.BooleanField(default=True, description="是否为当前头像")
    storage_type = fields.CharField(
        max_length=20, default="local", description="存储类型：local/oss/cos/qiniu"
    )
    cdn_url = fields.CharField(max_length=512, null=True, description="CDN访问URL")
    created_at = fields.DatetimeField(auto_now_add=True, description="上传时间")

    class Meta:
        table = "user_avatars"
        ordering = ["-created_at"]

    def __str__(self):
        return f"用户 {self.user.username} 头像: {self.original_filename}"


class EmailChangeRequest(models.Model):
    """邮箱更改请求表"""

    id = fields.IntField(pk=True)
    user: fields.ForeignKeyRelation[User] = fields.ForeignKeyField(
        "models.User",
        related_name="email_change_requests",
        on_delete=fields.CASCADE,
        description="关联用户",
    )
    old_email = fields.CharField(max_length=100, description="旧邮箱")
    new_email = fields.CharField(max_length=100, description="新邮箱")
    verification_token = fields.CharField(max_length=255, description="验证令牌")
    token_expires_at = fields.DatetimeField(description="令牌过期时间")
    is_verified = fields.BooleanField(default=False, description="是否已验证")
    verified_at = fields.DatetimeField(null=True, description="验证时间")
    ip_address = fields.CharField(max_length=45, null=True, description="请求IP地址")
    created_at = fields.DatetimeField(auto_now_add=True, description="创建时间")

    class Meta:
        table = "email_change_requests"
        ordering = ["-created_at"]

    def __str__(self):
        return (
            f"用户 {self.user.username} 邮箱更改: {self.old_email} -> {self.new_email}"
        )


# 创建Pydantic模型用于API响应
UserPydantic = pydantic_model_creator(
    User,
    name="User",
    exclude=("password_hash", "email_verify_token", "password_reset_token"),
)
UserInPydantic = pydantic_model_creator(
    User, name="UserIn", include=("username", "email", "password_hash", "nickname")
)
RolePydantic = pydantic_model_creator(Role, name="Role")
PointsTransactionPydantic = pydantic_model_creator(
    PointsTransaction, name="PointsTransaction"
)
