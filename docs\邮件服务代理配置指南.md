# 邮件服务代理配置指南

## 概述

由于国内网络环境限制，直接访问Gmail等国外邮件服务可能会遇到连接问题。本指南将帮助您配置代理来解决这个问题。

## 功能特性

- 支持多种代理类型：SOCKS5、SOCKS4、HTTP
- 自动降级：代理失败时自动尝试直连
- 完整的错误处理和日志记录
- 支持代理认证（用户名/密码）

## 配置步骤

### 1. Gmail 应用专用密码设置

首先，您需要为Gmail设置应用专用密码：

1. 登录您的Google账户
2. 进入 [Google账户安全设置](https://myaccount.google.com/security)
3. 启用两步验证（如果尚未启用）
4. 在"应用专用密码"部分生成新密码
5. 选择"邮件"应用类型
6. 复制生成的16位密码

### 2. 代理服务器准备

确保您有可用的代理服务器，常见选择：

- **本地代理软件**：如V2Ray、Clash、Shadowsocks等
- **VPN服务**：支持SOCKS5代理的VPN
- **商业代理服务**：购买的代理服务

### 3. 配置文件设置

在 `app/config.yaml` 中配置邮件服务：

```yaml
# 邮件服务配置
email:
  smtp_server: "smtp.gmail.com"    # SMTP服务器地址
  smtp_port: 587                   # SMTP端口 (587 for TLS, 465 for SSL)
  username: "<EMAIL>" # 您的Gmail地址
  password: "your-app-password"    # Gmail应用专用密码
  from_email: "<EMAIL>" # 发件人邮箱
  from_name: "Pan-So Team"         # 发件人名称
  
  # 代理配置
  proxy:
    enabled: true                  # 启用代理
    type: "socks5"                # 代理类型: socks5, socks4, http
    host: "127.0.0.1"             # 代理服务器地址
    port: 1080                    # 代理端口
    username: ""                  # 代理用户名 (可选)
    password: ""                  # 代理密码 (可选)
```

### 4. 常见代理配置示例

#### SOCKS5 代理（推荐）
```yaml
proxy:
  enabled: true
  type: "socks5"
  host: "127.0.0.1"
  port: 1080
```

#### HTTP 代理
```yaml
proxy:
  enabled: true
  type: "http"
  host: "127.0.0.1"
  port: 8080
```

#### 带认证的代理
```yaml
proxy:
  enabled: true
  type: "socks5"
  host: "proxy.example.com"
  port: 1080
  username: "your-username"
  password: "your-password"
```

## 测试配置

### 1. 运行测试

```bash
# 运行邮件服务测试
python -m pytest tests/test_email_service.py -v

# 或者运行所有测试
python -m pytest tests/ -v
```

### 2. 手动测试

创建一个简单的测试脚本：

```python
import asyncio
from app.core.email import EmailService

async def test_email():
    email_service = EmailService()
    
    if not email_service.enabled:
        print("邮件服务未启用，请检查配置")
        return
    
    try:
        await email_service.send_verification_email(
            "<EMAIL>", 
            "测试用户", 
            "test-token-123"
        )
        print("邮件发送成功！")
    except Exception as e:
        print(f"邮件发送失败: {e}")

if __name__ == "__main__":
    asyncio.run(test_email())
```

## 故障排除

### 常见问题

1. **代理连接失败**
   - 检查代理服务器是否正常运行
   - 验证代理地址和端口是否正确
   - 确认防火墙没有阻止连接

2. **Gmail认证失败**
   - 确认使用的是应用专用密码，不是账户密码
   - 检查Gmail账户是否启用了两步验证
   - 验证用户名和密码是否正确

3. **SMTP连接超时**
   - 检查网络连接
   - 尝试不同的代理服务器
   - 确认SMTP端口（587或465）是否正确

### 日志分析

邮件服务会记录详细的日志信息：

```
INFO - 邮件服务将使用代理: socks5://127.0.0.1:1080
INFO - 通过代理 socks5://127.0.0.1:1080 连接到SMTP服务器成功
INFO - 邮件发送成功 (代理): <EMAIL>
```

如果代理失败，系统会自动尝试直连：

```
ERROR - 邮件发送失败 (代理): <EMAIL>, 错误: Connection refused
INFO - 代理发送失败，尝试直连模式...
INFO - 邮件发送成功 (直连备用): <EMAIL>
```

## 安全建议

1. **保护配置文件**：确保config.yaml文件权限设置正确，避免密码泄露
2. **使用应用专用密码**：不要在配置中使用Gmail主密码
3. **定期更换密码**：定期更新应用专用密码
4. **监控日志**：定期检查邮件发送日志，发现异常及时处理

## 性能优化

1. **连接池**：对于大量邮件发送，考虑实现SMTP连接池
2. **异步处理**：邮件发送已经是异步的，避免阻塞主线程
3. **重试机制**：系统已内置代理失败时的直连重试机制
4. **监控指标**：记录邮件发送成功率和响应时间

## 支持的邮件服务

除了Gmail，本配置也支持其他邮件服务：

- **Outlook/Hotmail**: smtp-mail.outlook.com:587
- **Yahoo Mail**: smtp.mail.yahoo.com:587
- **QQ邮箱**: smtp.qq.com:587
- **163邮箱**: smtp.163.com:25

只需修改`smtp_server`和`smtp_port`配置即可。
