"""
头像存储服务 - Cloudflare R2版本

使用Cloudflare R2对象存储，支持：
1. 高性能全球CDN加速
2. 自动图片处理和优化
3. 统一400x400尺寸规格
4. 本地存储作为备用方案
"""

from fastapi import UploadFile
from typing import Dict, Any
from tortoise.transactions import in_transaction
import os
import uuid
from datetime import datetime
from PIL import Image
import logging
import httpx
from io import BytesIO

from app.models.user import User, UserAvatar
from app.utils.config import settings

logger = logging.getLogger(__name__)


class AvatarService:
    """头像存储服务类 - Cloudflare R2版本"""

    # 支持的图片格式
    ALLOWED_EXTENSIONS = {".jpg", ".jpeg", ".png", ".gif", ".webp"}
    ALLOWED_MIME_TYPES = {"image/jpeg", "image/png", "image/gif", "image/webp"}

    # 文件大小限制（5MB）
    MAX_FILE_SIZE = 5 * 1024 * 1024

    # 头像尺寸配置 - 统一使用400x400
    AVATAR_SIZE = (400, 400)

    @classmethod
    async def upload_avatar(cls, user: User, file: UploadFile) -> Dict[str, Any]:
        """上传用户头像"""

        # 验证文件
        await cls._validate_file(file)

        # 读取文件内容
        file_content = await file.read()

        # 处理图片：调整为400x400尺寸
        processed_content = await cls._process_image(file_content)

        # 生成文件路径
        file_path = cls._generate_file_path(user.id, file.filename)

        async with in_transaction():
            # 停用用户的所有旧头像
            await user.deactivate_all_avatars()

            # 保存文件到R2
            storage_info = await cls._save_to_r2(processed_content, file_path)

            # 创建头像记录
            avatar = await UserAvatar.create(
                user=user,
                original_filename=file.filename,
                file_path=storage_info["file_path"],
                file_size=len(processed_content),
                mime_type="image/webp",  # 统一转换为WebP格式
                width=cls.AVATAR_SIZE[0],
                height=cls.AVATAR_SIZE[1],
                is_active=True,
                storage_type=storage_info["storage_type"],
                cdn_url=storage_info.get("cdn_url"),
            )

            # 更新用户头像字段
            user.avatar = storage_info.get("cdn_url") or storage_info["file_path"]
            await user.save()

            logger.info(f"用户 {user.username} 上传了新头像: {file.filename}")

        # 事务完成后，异步清理旧头像文件（不影响主流程）
        try:
            await user.cleanup_old_avatars(keep_recent=1)
        except Exception as e:
            logger.error(f"清理旧头像失败，但不影响新头像上传: {e}")

        return {
            "avatar_id": avatar.id,
            "file_path": avatar.file_path,
            "cdn_url": avatar.cdn_url,
            "file_size": avatar.file_size,
            "width": avatar.width,
            "height": avatar.height,
            "mime_type": avatar.mime_type,
            "storage_type": avatar.storage_type,
        }

    @classmethod
    async def _validate_file(cls, file: UploadFile):
        """验证上传文件"""

        # 检查文件扩展名
        if file.filename:
            ext = os.path.splitext(file.filename)[1].lower()
            if ext not in cls.ALLOWED_EXTENSIONS:
                raise ValueError(
                    f"不支持的文件格式，支持的格式：{', '.join(cls.ALLOWED_EXTENSIONS)}"
                )

        # 检查MIME类型
        if file.content_type not in cls.ALLOWED_MIME_TYPES:
            raise ValueError(f"不支持的文件类型：{file.content_type}")

        # 检查文件大小
        file.file.seek(0, 2)  # 移动到文件末尾
        file_size = file.file.tell()
        file.file.seek(0)  # 重置到文件开头

        if file_size > cls.MAX_FILE_SIZE:
            raise ValueError(
                f"文件大小超过限制，最大允许 {cls.MAX_FILE_SIZE // (1024*1024)}MB"
            )

        if file_size == 0:
            raise ValueError("文件为空")

    @classmethod
    async def _process_image(cls, file_content: bytes) -> bytes:
        """处理图片：调整为400x400尺寸并优化"""
        try:
            # 打开图片
            image = Image.open(BytesIO(file_content))

            # 保持RGBA模式以支持WebP透明度
            if image.mode == "P":
                image = image.convert("RGBA")
            elif image.mode not in ("RGBA", "RGB"):
                image = image.convert("RGBA")

            # 调整尺寸为400x400，保持比例并居中裁剪
            image = cls._resize_and_crop(image, cls.AVATAR_SIZE)

            # 保存为WebP格式，质量80%
            output = BytesIO()
            image.save(output, format="WebP", quality=80, optimize=True)

            return output.getvalue()

        except Exception as e:
            logger.error(f"图片处理失败: {e}")
            raise ValueError("图片处理失败，请检查文件格式")

    @classmethod
    def _resize_and_crop(cls, image: Image.Image, target_size: tuple) -> Image.Image:
        """调整图片尺寸并居中裁剪"""
        target_width, target_height = target_size
        original_width, original_height = image.size

        # 计算缩放比例，保证图片能完全覆盖目标尺寸
        scale = max(target_width / original_width, target_height / original_height)

        # 计算缩放后的尺寸
        new_width = int(original_width * scale)
        new_height = int(original_height * scale)

        # 缩放图片
        image = image.resize((new_width, new_height), Image.Resampling.LANCZOS)

        # 计算裁剪位置（居中）
        left = (new_width - target_width) // 2
        top = (new_height - target_height) // 2
        right = left + target_width
        bottom = top + target_height

        # 裁剪图片
        return image.crop((left, top, right, bottom))

    @classmethod
    def _generate_file_path(cls, user_id: int, filename: str = None) -> str:
        """
        生成文件存储路径

        Args:
            user_id: 用户ID
            filename: 原始文件名（可选，仅用于日志记录）
        """

        # 生成唯一文件名
        unique_id = str(uuid.uuid4())
        timestamp = datetime.now().strftime("%Y%m%d")

        # 构建路径：avatars/年月日/用户ID/唯一ID.webp
        file_path = f"avatars/{timestamp}/{user_id}/{unique_id}.webp"

        return file_path

    @classmethod
    async def _save_to_r2(cls, file_content: bytes, file_path: str) -> Dict[str, Any]:
        """保存文件到Cloudflare R2"""

        # 获取R2配置
        r2_config = settings.get("avatar.r2", {})
        api_token = r2_config.get("api_token")
        account_id = r2_config.get("account_id")
        bucket_name = r2_config.get("bucket_name")

        # 优先使用自定义域名，如果没有则使用公共URL
        cdn_base_url = r2_config.get("custom_domain") or r2_config.get("public_url")

        if not all([api_token, account_id, bucket_name]):
            logger.error("R2配置不完整，回退到本地存储")
            return await cls._save_to_local_fallback(file_content, file_path)

        try:
            # 构建R2 API URL
            api_url = f"https://api.cloudflare.com/client/v4/accounts/{account_id}/r2/buckets/{bucket_name}/objects/{file_path}"

            # 设置请求头
            headers = {
                "Authorization": f"Bearer {api_token}",
                "Content-Type": "image/webp",
            }

            # 上传文件到R2
            async with httpx.AsyncClient(timeout=30.0) as client:
                response = await client.put(
                    api_url, content=file_content, headers=headers
                )

                if response.status_code == 200:
                    # 生成CDN URL，优先使用自定义域名
                    if cdn_base_url:
                        cdn_url = f"{cdn_base_url.rstrip('/')}/{file_path}"
                    else:
                        cdn_url = None
                        logger.warning("未配置CDN域名，无法生成访问URL")

                    logger.info(f"文件成功上传到R2: {file_path}")
                    if cdn_url:
                        logger.info(f"CDN访问URL: {cdn_url}")

                    return {
                        "storage_type": "r2",
                        "file_path": file_path,
                        "cdn_url": cdn_url,
                    }
                else:
                    logger.error(
                        f"R2上传失败: {response.status_code} - {response.text}"
                    )
                    raise Exception(f"R2上传失败: {response.status_code}")

        except Exception as e:
            logger.error(f"R2上传异常: {e}")
            # 回退到本地存储
            return await cls._save_to_local_fallback(file_content, file_path)

    @classmethod
    async def _save_to_local_fallback(
        cls, file_content: bytes, file_path: str
    ) -> Dict[str, Any]:
        """本地存储备用方案"""

        # 获取本地存储根目录
        upload_dir = settings.get("avatar.local_path", "uploads")
        full_path = os.path.join(upload_dir, file_path)

        # 确保目录存在
        os.makedirs(os.path.dirname(full_path), exist_ok=True)

        # 写入文件
        with open(full_path, "wb") as f:
            f.write(file_content)

        # 生成访问URL
        base_url = settings.get("avatar.base_url", "/uploads")
        access_url = f"{base_url}/{file_path}"

        logger.info(f"文件保存到本地存储: {full_path}")

        return {"storage_type": "local", "file_path": full_path, "cdn_url": access_url}

    @classmethod
    async def get_avatar_history(
        cls, user: User, page: int = 1, size: int = 20
    ) -> Dict[str, Any]:
        """获取用户头像历史"""

        offset = (page - 1) * size

        # 获取总数
        total = await UserAvatar.filter(user=user).count()

        # 获取头像记录
        avatars = (
            await UserAvatar.filter(user=user)
            .order_by("-created_at")
            .offset(offset)
            .limit(size)
        )

        # 计算总页数
        pages = (total + size - 1) // size

        return {
            "total": total,
            "page": page,
            "size": size,
            "pages": pages,
            "avatars": [
                {
                    "id": avatar.id,
                    "original_filename": avatar.original_filename,
                    "file_path": avatar.file_path,
                    "cdn_url": avatar.cdn_url,
                    "file_size": avatar.file_size,
                    "width": avatar.width,
                    "height": avatar.height,
                    "is_active": avatar.is_active,
                    "created_at": avatar.created_at,
                }
                for avatar in avatars
            ],
        }

    @classmethod
    async def delete_avatar(cls, user: User, avatar_id: int):
        """删除指定头像"""

        avatar = await UserAvatar.filter(id=avatar_id, user=user).first()
        if not avatar:
            raise ValueError("头像不存在")

        if avatar.is_active:
            raise ValueError("不能删除当前使用的头像")

        async with in_transaction():
            # 删除文件（根据存储类型）
            await cls._delete_file(avatar.file_path, avatar.storage_type)

            # 删除数据库记录
            await avatar.delete()

            logger.info(f"用户 {user.username} 删除了头像: {avatar.original_filename}")

    @classmethod
    async def _delete_file(cls, file_path: str, storage_type: str):
        """删除存储文件"""

        if storage_type == "local":
            try:
                if os.path.exists(file_path):
                    os.remove(file_path)
                    logger.info(f"删除本地文件: {file_path}")
            except Exception as e:
                logger.error(f"删除本地文件失败: {e}")

        elif storage_type == "r2":
            # 删除R2文件
            try:
                r2_config = settings.get("avatar.r2", {})
                api_token = r2_config.get("api_token")
                account_id = r2_config.get("account_id")
                bucket_name = r2_config.get("bucket_name")

                if all([api_token, account_id, bucket_name]):
                    api_url = f"https://api.cloudflare.com/client/v4/accounts/{account_id}/r2/buckets/{bucket_name}/objects/{file_path}"

                    headers = {"Authorization": f"Bearer {api_token}"}

                    async with httpx.AsyncClient(timeout=30.0) as client:
                        response = await client.delete(api_url, headers=headers)

                        if response.status_code == 200:
                            logger.info(f"删除R2文件: {file_path}")
                        else:
                            logger.error(f"删除R2文件失败: {response.status_code}")

            except Exception as e:
                logger.error(f"删除R2文件异常: {e}")
