import smtplib
import asyncio
from email.mime.text import MIMEText
from email.mime.multipart import MIMEMultipart
from jinja2 import Environment, FileSystemLoader, Template
from typing import Optional
import logging
from app.utils.config import settings

logger = logging.getLogger(__name__)


class EmailService:
    """邮箱服务"""

    def __init__(self):
        self.smtp_server = settings.get("email.smtp_server", "smtp.gmail.com")
        self.smtp_port = settings.get("email.smtp_port", 587)
        self.username = settings.get("email.username")
        self.password = settings.get("email.password")
        self.from_email = settings.get("email.from_email", "<EMAIL>")
        self.from_name = settings.get("email.from_name", "97盘搜团队")

        # 检查邮箱配置
        if not self.username or not self.password:
            logger.warning("邮箱服务未配置，邮件发送功能将不可用")
            self.enabled = False
        else:
            self.enabled = True

        # 初始化模板引擎
        try:
            self.template_env = Environment(
                loader=FileSystemLoader("app/templates/email")
            )
        except Exception as e:
            logger.warning(f"邮件模板目录不存在，使用内置模板: {e}")
            self.template_env = None

    def _get_verification_template(self) -> str:
        """获取邮箱验证模板"""
        return """
        <!DOCTYPE html>
        <html>
        <head>
            <meta charset="utf-8">
            <title>邮箱验证 - {{ app_name }}</title>
            <style>
                body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
                .container { max-width: 600px; margin: 0 auto; padding: 20px; }
                .header { background: #007bff; color: white; padding: 20px; text-align: center; }
                .content { padding: 20px; background: #f9f9f9; }
                .button { display: inline-block; padding: 12px 24px; background: #007bff; color: white; text-decoration: none; border-radius: 4px; }
                .footer { padding: 20px; text-align: center; color: #666; font-size: 12px; }
            </style>
        </head>
        <body>
            <div class="container">
                <div class="header">
                    <h1>{{ app_name }}</h1>
                </div>
                <div class="content">
                    <h2>邮箱验证</h2>
                    <p>亲爱的 {{ username }}，</p>
                    <p>感谢您注册 {{ app_name }}！请点击下面的按钮验证您的邮箱地址：</p>
                    <p style="text-align: center; margin: 30px 0;">
                        <a href="{{ verify_url }}" class="button">验证邮箱</a>
                    </p>
                    <p>如果按钮无法点击，请复制以下链接到浏览器地址栏：</p>
                    <p style="word-break: break-all; color: #007bff;">{{ verify_url }}</p>
                    <p>此链接将在24小时后失效。</p>
                    <p>如果您没有注册账户，请忽略此邮件。</p>
                </div>
                <div class="footer">
                    <p>此邮件由系统自动发送，请勿回复。</p>
                    <p>&copy; {{ app_name }} 团队</p>
                </div>
            </div>
        </body>
        </html>
        """

    def _get_password_reset_template(self) -> str:
        """获取密码重置模板"""
        return """
        <!DOCTYPE html>
        <html>
        <head>
            <meta charset="utf-8">
            <title>密码重置 - {{ app_name }}</title>
            <style>
                body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
                .container { max-width: 600px; margin: 0 auto; padding: 20px; }
                .header { background: #dc3545; color: white; padding: 20px; text-align: center; }
                .content { padding: 20px; background: #f9f9f9; }
                .button { display: inline-block; padding: 12px 24px; background: #dc3545; color: white; text-decoration: none; border-radius: 4px; }
                .footer { padding: 20px; text-align: center; color: #666; font-size: 12px; }
            </style>
        </head>
        <body>
            <div class="container">
                <div class="header">
                    <h1>{{ app_name }}</h1>
                </div>
                <div class="content">
                    <h2>密码重置</h2>
                    <p>亲爱的 {{ username }}，</p>
                    <p>我们收到了您的密码重置请求。请点击下面的按钮重置您的密码：</p>
                    <p style="text-align: center; margin: 30px 0;">
                        <a href="{{ reset_url }}" class="button">重置密码</a>
                    </p>
                    <p>如果按钮无法点击，请复制以下链接到浏览器地址栏：</p>
                    <p style="word-break: break-all; color: #dc3545;">{{ reset_url }}</p>
                    <p>此链接将在24小时后失效。</p>
                    <p>如果您没有请求重置密码，请忽略此邮件，您的密码不会被更改。</p>
                </div>
                <div class="footer">
                    <p>此邮件由系统自动发送，请勿回复。</p>
                    <p>&copy; {{ app_name }} 团队</p>
                </div>
            </div>
        </body>
        </html>
        """

    async def send_verification_email(
        self, to_email: str, username: str, verify_token: str
    ):
        """发送邮箱验证邮件"""
        if not self.enabled:
            logger.warning("邮箱服务未启用，跳过发送验证邮件")
            return

        # 开发环境下模拟发送邮件
        if settings.get("app.debug", True):
            logger.info(f"[开发模式] 模拟发送验证邮件到: {to_email}")
            logger.info(f"[开发模式] 验证令牌: {verify_token}")
            frontend_url = settings.get("app.frontend_url", "http://localhost:3000")
            verify_url = f"{frontend_url}/verify-email?token={verify_token}"
            logger.info(f"[开发模式] 验证链接: {verify_url}")
            return

        frontend_url = settings.get("app.frontend_url", "http://localhost:3000")
        verify_url = f"{frontend_url}/verify-email?token={verify_token}"
        app_name = settings.get("app.name", "97盘搜")

        # 渲染模板
        if self.template_env:
            try:
                template = self.template_env.get_template("verification.html")
                html_content = template.render(
                    username=username, verify_url=verify_url, app_name=app_name
                )
            except Exception as e:
                logger.warning(f"使用文件模板失败，使用内置模板: {e}")
                template = Template(self._get_verification_template())
                html_content = template.render(
                    username=username, verify_url=verify_url, app_name=app_name
                )
        else:
            template = Template(self._get_verification_template())
            html_content = template.render(
                username=username, verify_url=verify_url, app_name=app_name
            )

        await self._send_email(
            to_email=to_email,
            subject=f"邮箱验证 - {app_name}",
            html_content=html_content,
        )

    async def send_password_reset_email(
        self, to_email: str, username: str, reset_token: str
    ):
        """发送密码重置邮件"""
        if not self.enabled:
            logger.warning("邮箱服务未启用，跳过发送密码重置邮件")
            return

        # 开发环境下模拟发送邮件
        if settings.get("app.debug", True):
            logger.info(f"[开发模式] 模拟发送密码重置邮件到: {to_email}")
            logger.info(f"[开发模式] 重置令牌: {reset_token}")
            frontend_url = settings.get("app.frontend_url", "http://localhost:3000")
            reset_url = f"{frontend_url}/reset-password?token={reset_token}"
            logger.info(f"[开发模式] 重置链接: {reset_url}")
            return

        frontend_url = settings.get("app.frontend_url", "http://localhost:3000")
        reset_url = f"{frontend_url}/reset-password?token={reset_token}"
        app_name = settings.get("app.name", "97盘搜")

        # 渲染模板
        if self.template_env:
            try:
                template = self.template_env.get_template("password_reset.html")
                html_content = template.render(
                    username=username, reset_url=reset_url, app_name=app_name
                )
            except Exception as e:
                logger.warning(f"使用文件模板失败，使用内置模板: {e}")
                template = Template(self._get_password_reset_template())
                html_content = template.render(
                    username=username, reset_url=reset_url, app_name=app_name
                )
        else:
            template = Template(self._get_password_reset_template())
            html_content = template.render(
                username=username, reset_url=reset_url, app_name=app_name
            )

        await self._send_email(
            to_email=to_email,
            subject=f"密码重置 - {app_name}",
            html_content=html_content,
        )

    async def _send_email(self, to_email: str, subject: str, html_content: str):
        """发送邮件的通用方法"""
        if not self.enabled:
            logger.warning("邮箱服务未启用")
            return

        def send_sync():
            """同步发送邮件"""
            server = None
            try:
                msg = MIMEMultipart("alternative")
                msg["Subject"] = subject
                msg["From"] = f"{self.from_name} <{self.from_email}>"
                msg["To"] = to_email

                html_part = MIMEText(html_content, "html", "utf-8")
                msg.attach(html_part)

                # 创建SMTP连接
                server = smtplib.SMTP(self.smtp_server, self.smtp_port, timeout=15)
                logger.info(f"SMTP连接已建立: {self.smtp_server}:{self.smtp_port}")

                # 启动TLS加密
                server.starttls()
                logger.info("TLS加密已启动")

                # 登录认证
                server.login(self.username, self.password)
                logger.info("SMTP认证成功")

                # 发送邮件
                send_result = server.send_message(msg)
                logger.info(f"邮件发送命令执行完成，结果: {send_result}")

                # 检查发送结果
                if send_result:
                    # 如果有返回值，说明部分收件人失败
                    logger.warning(f"部分收件人发送失败: {send_result}")
                else:
                    logger.info(f"邮件发送成功: {to_email}")

            except smtplib.SMTPException as e:
                logger.error(f"SMTP错误: {to_email}, 错误: {str(e)}")
                # 对于特定的SMTP错误，我们需要判断是否实际发送成功
                if "(-1, b'\\x00\\x00\\x00')" in str(e):
                    logger.warning(f"收到特殊SMTP响应，邮件可能已发送成功: {to_email}")
                    # 不抛出异常，因为邮件可能已经发送成功
                    return
                raise Exception(f"SMTP发送失败: {str(e)}")
            except Exception as e:
                logger.error(f"邮件发送失败: {to_email}, 错误: {str(e)}")
                raise Exception(f"邮件发送失败: {str(e)}")
            finally:
                # 安全关闭连接
                if server:
                    try:
                        server.quit()
                        logger.info("SMTP连接已正常关闭")
                    except Exception as quit_e:
                        logger.warning(f"SMTP连接关闭时出现问题: {quit_e}")
                        # 尝试强制关闭
                        try:
                            server.close()
                        except:
                            pass

        # 在线程池中执行同步邮件发送
        loop = asyncio.get_event_loop()
        await loop.run_in_executor(None, send_sync)

    async def send_email_change_verification(
        self,
        to_email: str,
        username: str,
        old_email: str,
        new_email: str,
        verify_token: str,
    ):
        """发送邮箱更改验证邮件"""
        app_name = settings.get("app.name", "97盘搜")

        if self.template_env:
            try:
                template = self.template_env.get_template(
                    "email_change_verification.html"
                )
                html_content = template.render(
                    username=username,
                    old_email=old_email,
                    new_email=new_email,
                    verify_url=f"{settings.get('app.frontend_url', 'http://localhost:3000')}/verify-email-change?token={verify_token}",
                    app_name=app_name,
                )
            except Exception as e:
                logger.warning(f"使用文件模板失败，使用内置模板: {e}")
                template = Template(self._get_email_change_verification_template())
                html_content = template.render(
                    username=username,
                    old_email=old_email,
                    new_email=new_email,
                    verify_url=f"{settings.get('app.frontend_url', 'http://localhost:3000')}/verify-email-change?token={verify_token}",
                    app_name=app_name,
                )
        else:
            template = Template(self._get_email_change_verification_template())
            html_content = template.render(
                username=username,
                old_email=old_email,
                new_email=new_email,
                verify_url=f"{settings.get('app.frontend_url', 'http://localhost:3000')}/verify-email-change?token={verify_token}",
                app_name=app_name,
            )

        await self._send_email(
            to_email=to_email,
            subject=f"验证您的新邮箱 - {app_name}",
            html_content=html_content,
        )

    async def send_email_change_confirmation(
        self, to_email: str, username: str, old_email: str, new_email: str
    ):
        """发送邮箱更改成功确认邮件"""
        app_name = settings.get("app.name", "97盘搜")

        template = Template(self._get_email_change_confirmation_template())
        html_content = template.render(
            username=username,
            old_email=old_email,
            new_email=new_email,
            app_name=app_name,
        )

        await self._send_email(
            to_email=to_email,
            subject=f"邮箱更改成功 - {app_name}",
            html_content=html_content,
        )

    async def send_email_change_notification(
        self, to_email: str, username: str, old_email: str, new_email: str
    ):
        """发送邮箱更改通知邮件到旧邮箱"""
        app_name = settings.get("app.name", "97盘搜")

        template = Template(self._get_email_change_notification_template())
        html_content = template.render(
            username=username,
            old_email=old_email,
            new_email=new_email,
            app_name=app_name,
        )

        await self._send_email(
            to_email=to_email,
            subject=f"您的账户邮箱已更改 - {app_name}",
            html_content=html_content,
        )

    def _get_email_change_verification_template(self) -> str:
        """邮箱更改验证邮件模板"""
        return """
        <!DOCTYPE html>
        <html>
        <head>
            <meta charset="utf-8">
            <title>验证您的新邮箱</title>
            <style>
                body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
                .container { max-width: 600px; margin: 0 auto; padding: 20px; }
                .header { background: #007bff; color: white; padding: 20px; text-align: center; }
                .content { padding: 20px; background: #f9f9f9; }
                .button { display: inline-block; padding: 12px 24px; background: #007bff; color: white; text-decoration: none; border-radius: 4px; }
                .footer { text-align: center; padding: 20px; color: #666; font-size: 12px; }
            </style>
        </head>
        <body>
            <div class="container">
                <div class="header">
                    <h1>验证您的新邮箱</h1>
                </div>
                <div class="content">
                    <p>亲爱的 {{ username }}，</p>
                    <p>您请求将账户邮箱从 <strong>{{ old_email }}</strong> 更改为 <strong>{{ new_email }}</strong>。</p>
                    <p>为了确保安全，请点击下面的按钮验证您的新邮箱：</p>
                    <p style="text-align: center; margin: 30px 0;">
                        <a href="{{ verify_url }}" class="button">验证新邮箱</a>
                    </p>
                    <p>如果按钮无法点击，请复制以下链接到浏览器地址栏：</p>
                    <p style="word-break: break-all; color: #007bff;">{{ verify_url }}</p>
                    <p>此链接将在24小时后失效。</p>
                    <p>如果您没有请求更改邮箱，请忽略此邮件。</p>
                </div>
                <div class="footer">
                    <p>此邮件由系统自动发送，请勿回复。</p>
                    <p>&copy; {{ app_name }} 团队</p>
                </div>
            </div>
        </body>
        </html>
        """

    def _get_email_change_confirmation_template(self) -> str:
        """邮箱更改成功确认邮件模板"""
        return """
        <!DOCTYPE html>
        <html>
        <head>
            <meta charset="utf-8">
            <title>邮箱更改成功</title>
            <style>
                body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
                .container { max-width: 600px; margin: 0 auto; padding: 20px; }
                .header { background: #28a745; color: white; padding: 20px; text-align: center; }
                .content { padding: 20px; background: #f9f9f9; }
                .footer { text-align: center; padding: 20px; color: #666; font-size: 12px; }
            </style>
        </head>
        <body>
            <div class="container">
                <div class="header">
                    <h1>邮箱更改成功</h1>
                </div>
                <div class="content">
                    <p>亲爱的 {{ username }}，</p>
                    <p>您的账户邮箱已成功从 <strong>{{ old_email }}</strong> 更改为 <strong>{{ new_email }}</strong>。</p>
                    <p>从现在开始，请使用新邮箱登录您的账户。</p>
                    <p>如果您没有进行此操作，请立即联系我们的客服。</p>
                </div>
                <div class="footer">
                    <p>此邮件由系统自动发送，请勿回复。</p>
                    <p>&copy; {{ app_name }} 团队</p>
                </div>
            </div>
        </body>
        </html>
        """

    def _get_email_change_notification_template(self) -> str:
        """邮箱更改通知邮件模板（发送到旧邮箱）"""
        return """
        <!DOCTYPE html>
        <html>
        <head>
            <meta charset="utf-8">
            <title>您的账户邮箱已更改</title>
            <style>
                body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
                .container { max-width: 600px; margin: 0 auto; padding: 20px; }
                .header { background: #ffc107; color: #212529; padding: 20px; text-align: center; }
                .content { padding: 20px; background: #f9f9f9; }
                .footer { text-align: center; padding: 20px; color: #666; font-size: 12px; }
            </style>
        </head>
        <body>
            <div class="container">
                <div class="header">
                    <h1>账户邮箱已更改</h1>
                </div>
                <div class="content">
                    <p>亲爱的 {{ username }}，</p>
                    <p>您的账户邮箱已从 <strong>{{ old_email }}</strong> 更改为 <strong>{{ new_email }}</strong>。</p>
                    <p>如果这是您本人的操作，请忽略此邮件。</p>
                    <p>如果您没有进行此操作，说明您的账户可能存在安全风险，请立即联系我们的客服。</p>
                </div>
                <div class="footer">
                    <p>此邮件由系统自动发送，请勿回复。</p>
                    <p>&copy; {{ app_name }} 团队</p>
                </div>
            </div>
        </body>
        </html>
        """


# 全局邮箱服务实例
email_service = EmailService()
