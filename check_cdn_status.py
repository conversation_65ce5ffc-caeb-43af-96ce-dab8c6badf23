#!/usr/bin/env python3
"""
检查Cloudflare CDN状态和配置
"""

import asyncio
import httpx
from app.utils.config import settings

async def check_cdn_headers():
    """检查CDN响应头"""
    print("🌐 检查CDN响应头...")
    
    r2_config = settings.get("avatar.r2", {})
    custom_domain = r2_config.get("custom_domain")
    
    if not custom_domain:
        print("❌ 未配置自定义域名")
        return False
    
    # 测试URL（使用之前上传的文件）
    test_url = f"{custom_domain}/test/custom_domain_test.webp"
    
    try:
        async with httpx.AsyncClient(timeout=30.0) as client:
            response = await client.head(test_url)
            
            print(f"测试URL: {test_url}")
            print(f"状态码: {response.status_code}")
            
            # 检查CDN相关头部
            cdn_headers = {
                'cf-cache-status': 'Cloudflare缓存状态',
                'cf-ray': 'Cloudflare Ray ID',
                'server': '服务器类型',
                'cache-control': '缓存控制',
                'etag': 'ETag',
                'last-modified': '最后修改时间',
                'content-type': '内容类型',
                'cf-edge-cache': '边缘缓存',
                'age': '缓存年龄'
            }
            
            print("\n📋 CDN响应头分析:")
            for header, description in cdn_headers.items():
                value = response.headers.get(header)
                if value:
                    print(f"✅ {description}: {value}")
                else:
                    print(f"⚪ {description}: 未设置")
            
            # 分析缓存状态
            cache_status = response.headers.get('cf-cache-status')
            if cache_status:
                cache_meanings = {
                    'HIT': '✅ 缓存命中 - 从CDN边缘节点返回',
                    'MISS': '⚠️  缓存未命中 - 从源站获取',
                    'EXPIRED': '⚠️  缓存过期 - 需要重新验证',
                    'BYPASS': '⚠️  绕过缓存 - 直接从源站获取',
                    'DYNAMIC': '⚠️  动态内容 - 不缓存'
                }
                meaning = cache_meanings.get(cache_status, f"未知状态: {cache_status}")
                print(f"\n🎯 缓存状态分析: {meaning}")
            
            # 检查是否通过Cloudflare
            cf_ray = response.headers.get('cf-ray')
            if cf_ray:
                print(f"✅ 请求通过Cloudflare CDN处理 (Ray ID: {cf_ray})")
                return True
            else:
                print("❌ 请求未通过Cloudflare CDN")
                return False
                
    except Exception as e:
        print(f"❌ 检查失败: {e}")
        return False

async def check_domain_setup():
    """检查域名设置"""
    print("\n🔧 检查域名设置...")
    
    r2_config = settings.get("avatar.r2", {})
    custom_domain = r2_config.get("custom_domain")
    
    if not custom_domain:
        print("❌ 未配置自定义域名")
        return False
    
    domain = custom_domain.replace("https://", "").replace("http://", "")
    print(f"自定义域名: {domain}")
    
    try:
        # 检查DNS解析
        import socket
        ip = socket.gethostbyname(domain)
        print(f"DNS解析: {domain} -> {ip}")
        
        # 检查是否解析到Cloudflare
        # Cloudflare的IP段通常以这些开头
        cf_ip_ranges = ['104.', '172.', '108.', '198.', '162.', '173.', '188.']
        is_cloudflare = any(ip.startswith(prefix) for prefix in cf_ip_ranges)
        
        if is_cloudflare:
            print("✅ 域名解析到Cloudflare网络")
        else:
            print(f"⚠️  域名解析到: {ip} (可能不是Cloudflare)")
        
        return True
        
    except Exception as e:
        print(f"❌ DNS检查失败: {e}")
        return False

async def performance_test():
    """性能测试"""
    print("\n⚡ 性能测试...")
    
    r2_config = settings.get("avatar.r2", {})
    custom_domain = r2_config.get("custom_domain")
    public_url = r2_config.get("public_url")
    
    if not custom_domain:
        print("❌ 未配置自定义域名")
        return False
    
    # 测试URL
    test_path = "test/custom_domain_test.webp"
    urls = [
        (f"{custom_domain}/{test_path}", "自定义域名"),
        (f"{public_url}/{test_path}", "R2公共URL")
    ]
    
    results = []
    
    for url, name in urls:
        try:
            import time
            start_time = time.time()
            
            async with httpx.AsyncClient(timeout=30.0) as client:
                response = await client.head(url)
                
            end_time = time.time()
            response_time = (end_time - start_time) * 1000  # 转换为毫秒
            
            print(f"{name}: {response_time:.2f}ms (状态: {response.status_code})")
            results.append((name, response_time, response.status_code))
            
        except Exception as e:
            print(f"{name}: 失败 - {e}")
            results.append((name, float('inf'), 0))
    
    # 比较性能
    if len(results) == 2:
        custom_time = results[0][1]
        public_time = results[1][1]
        
        if custom_time < public_time:
            improvement = ((public_time - custom_time) / public_time) * 100
            print(f"✅ 自定义域名比公共URL快 {improvement:.1f}%")
        elif custom_time > public_time:
            degradation = ((custom_time - public_time) / custom_time) * 100
            print(f"⚠️  自定义域名比公共URL慢 {degradation:.1f}%")
        else:
            print("⚪ 性能相当")
    
    return True

async def main():
    """主函数"""
    print("🚀 Cloudflare CDN状态检查")
    print("=" * 50)
    
    # 显示配置
    r2_config = settings.get("avatar.r2", {})
    print("📋 当前配置:")
    print(f"  自定义域名: {r2_config.get('custom_domain')}")
    print(f"  公共URL: {r2_config.get('public_url')}")
    print(f"  存储桶: {r2_config.get('bucket_name')}")
    
    # 运行检查
    tests = [
        ("域名设置检查", check_domain_setup),
        ("CDN响应头检查", check_cdn_headers),
        ("性能测试", performance_test),
    ]
    
    results = []
    for test_name, test_func in tests:
        try:
            result = await test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name}异常: {e}")
            results.append((test_name, False))
    
    # 输出结果
    print("\n" + "=" * 50)
    print("📊 CDN状态汇总")
    print("=" * 50)
    
    passed = 0
    for test_name, result in results:
        status = "✅ 正常" if result else "❌ 异常"
        print(f"{test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\n总体状态: {passed}/{len(results)} 项正常")
    
    # 给出建议
    print("\n💡 优化建议:")
    print("1. 在Cloudflare控制台设置缓存规则:")
    print("   - 图片文件缓存7天")
    print("   - 启用Brotli压缩")
    print("   - 启用Polish图片优化")
    print("\n2. 监控CDN命中率:")
    print("   - 查看Cloudflare Analytics")
    print("   - 优化缓存策略")
    print("\n3. 考虑启用:")
    print("   - HTTP/2推送")
    print("   - 0-RTT连接")
    print("   - 自动HTTPS重定向")

if __name__ == "__main__":
    asyncio.run(main())
