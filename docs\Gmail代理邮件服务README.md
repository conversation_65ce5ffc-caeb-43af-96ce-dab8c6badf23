# Gmail代理邮件服务

## 概述

本项目已成功集成了支持代理的Gmail邮件服务，解决了国内服务器无法直接访问Gmail SMTP服务的问题。

## 主要特性

✅ **多代理支持**: 支持SOCKS5、SOCKS4、HTTP代理  
✅ **自动降级**: 代理失败时自动尝试直连  
✅ **完整认证**: 支持代理用户名/密码认证  
✅ **错误处理**: 详细的错误日志和异常处理  
✅ **异步支持**: 完全异步的邮件发送  
✅ **模板系统**: 内置邮件模板和自定义模板支持  
✅ **测试覆盖**: 完整的单元测试覆盖  

## 文件结构

```
├── app/
│   ├── core/
│   │   └── email.py              # 增强的邮件服务（支持代理）
│   └── config.yaml               # 配置文件（已添加邮件和代理配置）
├── tests/
│   └── test_email_service.py     # 邮件服务测试
├── tools/
│   └── test_email.py             # 邮件测试工具
├── examples/
│   └── email_proxy_example.py    # 使用示例
└── docs/
    ├── 邮件服务代理配置指南.md    # 详细配置指南
    └── Gmail代理邮件服务README.md # 本文件
```

## 快速开始

### 1. 配置Gmail应用专用密码

1. 登录Google账户
2. 启用两步验证
3. 生成应用专用密码
4. 复制16位密码

### 2. 配置代理服务器

确保您有可用的代理服务器（如V2Ray、Clash等）

### 3. 修改配置文件

编辑 `app/config.yaml`:

```yaml
email:
  smtp_server: "smtp.gmail.com"
  smtp_port: 587
  username: "<EMAIL>"    # 您的Gmail地址
  password: "your-app-password"       # Gmail应用专用密码
  from_email: "<EMAIL>"
  from_name: "Pan-So Team"
  
  proxy:
    enabled: true                     # 启用代理
    type: "socks5"                   # 代理类型
    host: "127.0.0.1"                # 代理地址
    port: 1080                       # 代理端口
    username: ""                     # 代理用户名（可选）
    password: ""                     # 代理密码（可选）
```

### 4. 测试配置

```bash
# 运行测试工具
python tools/test_email.py

# 运行单元测试
python -m pytest tests/test_email_service.py -v

# 运行示例
python examples/email_proxy_example.py
```

## 使用方法

### 基础邮件发送

```python
from app.core.email import EmailService

email_service = EmailService()

await email_service._send_email(
    to_email="<EMAIL>",
    subject="测试邮件",
    html_content="<h1>Hello World</h1>"
)
```

### 发送验证邮件

```python
await email_service.send_verification_email(
    to_email="<EMAIL>",
    username="用户名",
    verify_token="verification-token"
)
```

### 发送密码重置邮件

```python
await email_service.send_password_reset_email(
    to_email="<EMAIL>",
    username="用户名",
    reset_token="reset-token"
)
```

## 代理配置示例

### SOCKS5代理（推荐）
```yaml
proxy:
  enabled: true
  type: "socks5"
  host: "127.0.0.1"
  port: 1080
```

### HTTP代理
```yaml
proxy:
  enabled: true
  type: "http"
  host: "127.0.0.1"
  port: 8080
```

### 带认证的代理
```yaml
proxy:
  enabled: true
  type: "socks5"
  host: "proxy.example.com"
  port: 1080
  username: "proxy-user"
  password: "proxy-pass"
```

## 故障排除

### 常见问题

1. **代理连接失败**
   - 检查代理服务器状态
   - 验证代理地址和端口
   - 确认防火墙设置

2. **Gmail认证失败**
   - 使用应用专用密码，不是账户密码
   - 确认两步验证已启用
   - 检查用户名和密码

3. **连接超时**
   - 检查网络连接
   - 尝试不同代理服务器
   - 验证SMTP端口设置

### 日志分析

系统会记录详细日志：

```
INFO - 邮件服务将使用代理: socks5://127.0.0.1:1080
INFO - 通过代理连接到SMTP服务器成功
INFO - 邮件发送成功 (代理): <EMAIL>
```

代理失败时的自动降级：

```
ERROR - 邮件发送失败 (代理): Connection refused
INFO - 代理发送失败，尝试直连模式...
INFO - 邮件发送成功 (直连备用): <EMAIL>
```

## 测试

运行完整测试套件：

```bash
# 单元测试
python -m pytest tests/test_email_service.py -v

# 集成测试
python tools/test_email.py

# 示例测试
python examples/email_proxy_example.py
```

## 安全建议

1. **保护配置文件**: 设置正确的文件权限
2. **使用应用专用密码**: 不要使用主密码
3. **定期更换密码**: 定期更新应用专用密码
4. **监控日志**: 定期检查邮件发送日志

## 性能优化

1. **异步处理**: 邮件发送不会阻塞主线程
2. **连接复用**: 考虑实现连接池（未来优化）
3. **重试机制**: 内置代理失败重试
4. **监控指标**: 记录发送成功率

## 支持的邮件服务

除Gmail外，也支持其他邮件服务：

- **Outlook**: smtp-mail.outlook.com:587
- **Yahoo**: smtp.mail.yahoo.com:587
- **QQ邮箱**: smtp.qq.com:587
- **163邮箱**: smtp.163.com:25

## 依赖包

- `PySocks`: SOCKS代理支持（已在requirements.txt中）
- `smtplib`: Python内置SMTP库
- `jinja2`: 邮件模板引擎

## 更新日志

### v1.0.0 (2025-01-03)
- ✅ 添加代理支持（SOCKS5/SOCKS4/HTTP）
- ✅ 实现自动降级机制
- ✅ 完善错误处理和日志
- ✅ 添加完整测试覆盖
- ✅ 创建配置指南和示例

## 贡献

欢迎提交Issue和Pull Request来改进这个功能。

## 许可证

本项目遵循项目主许可证。
