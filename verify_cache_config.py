#!/usr/bin/env python3
"""
验证缓存配置效果
"""

import asyncio
import httpx
from app.utils.config import settings

async def verify_cache_rules():
    """验证缓存规则是否生效"""
    print("🔍 验证缓存规则配置...")
    
    r2_config = settings.get("avatar.r2", {})
    custom_domain = r2_config.get("custom_domain")
    
    if not custom_domain:
        print("❌ 未配置自定义域名")
        return False
    
    test_url = f"{custom_domain}/test/custom_domain_test.webp"
    
    try:
        async with httpx.AsyncClient(timeout=30.0) as client:
            print(f"测试URL: {test_url}")
            
            # 第一次请求 - 应该是MISS
            print("\n🔄 第一次请求（预期：MISS）:")
            response1 = await client.head(test_url)
            cache_status1 = response1.headers.get('cf-cache-status', 'UNKNOWN')
            cache_control1 = response1.headers.get('cache-control', '未设置')
            print(f"  缓存状态: {cache_status1}")
            print(f"  缓存控制: {cache_control1}")
            
            # 等待2秒后第二次请求 - 应该是HIT
            await asyncio.sleep(2)
            
            print("\n🎯 第二次请求（预期：HIT）:")
            response2 = await client.head(test_url)
            cache_status2 = response2.headers.get('cf-cache-status', 'UNKNOWN')
            cache_control2 = response2.headers.get('cache-control', '未设置')
            age = response2.headers.get('age', '未设置')
            print(f"  缓存状态: {cache_status2}")
            print(f"  缓存控制: {cache_control2}")
            print(f"  缓存年龄: {age}")
            
            # 分析结果
            if cache_status2 == 'HIT':
                print("\n✅ 缓存配置成功！")
                print("   图片已被CDN缓存，后续访问将更快")
                return True
            elif cache_status1 == 'MISS' and cache_status2 == 'MISS':
                print("\n⚠️  缓存规则可能未生效")
                print("   请检查页面规则配置")
                return False
            elif cache_status1 == 'DYNAMIC' and cache_status2 == 'DYNAMIC':
                print("\n❌ 仍被识别为动态内容")
                print("   需要设置更强的缓存规则")
                return False
            else:
                print(f"\n⚪ 缓存状态变化: {cache_status1} → {cache_status2}")
                return True
                
    except Exception as e:
        print(f"❌ 验证失败: {e}")
        return False

async def performance_comparison():
    """性能对比测试"""
    print("\n⚡ 性能对比测试...")
    
    r2_config = settings.get("avatar.r2", {})
    custom_domain = r2_config.get("custom_domain")
    
    if not custom_domain:
        print("❌ 未配置自定义域名")
        return False
    
    test_url = f"{custom_domain}/test/custom_domain_test.webp"
    
    try:
        import time
        
        async with httpx.AsyncClient(timeout=30.0) as client:
            # 第一次请求（冷缓存）
            print("🥶 冷缓存请求:")
            start_time = time.time()
            response1 = await client.head(test_url)
            cold_time = (time.time() - start_time) * 1000
            cache_status1 = response1.headers.get('cf-cache-status', 'UNKNOWN')
            print(f"  响应时间: {cold_time:.2f}ms")
            print(f"  缓存状态: {cache_status1}")
            
            # 等待1秒
            await asyncio.sleep(1)
            
            # 第二次请求（热缓存）
            print("\n🔥 热缓存请求:")
            start_time = time.time()
            response2 = await client.head(test_url)
            hot_time = (time.time() - start_time) * 1000
            cache_status2 = response2.headers.get('cf-cache-status', 'UNKNOWN')
            print(f"  响应时间: {hot_time:.2f}ms")
            print(f"  缓存状态: {cache_status2}")
            
            # 性能分析
            if cache_status2 == 'HIT' and hot_time < cold_time:
                improvement = ((cold_time - hot_time) / cold_time) * 100
                print(f"\n✅ 缓存提升性能 {improvement:.1f}%")
                print(f"   冷缓存: {cold_time:.2f}ms → 热缓存: {hot_time:.2f}ms")
                return True
            elif cache_status2 == 'HIT':
                print(f"\n✅ 缓存命中，但性能提升不明显")
                return True
            else:
                print(f"\n⚠️  缓存未命中，性能无提升")
                return False
                
    except Exception as e:
        print(f"❌ 性能测试失败: {e}")
        return False

async def check_compression():
    """检查压缩设置"""
    print("\n🗜️  检查压缩设置...")
    
    r2_config = settings.get("avatar.r2", {})
    custom_domain = r2_config.get("custom_domain")
    
    if not custom_domain:
        print("❌ 未配置自定义域名")
        return False
    
    test_url = f"{custom_domain}/test/custom_domain_test.webp"
    
    try:
        # 请求时指定接受压缩
        headers = {
            'Accept-Encoding': 'gzip, deflate, br'
        }
        
        async with httpx.AsyncClient(timeout=30.0) as client:
            response = await client.head(test_url, headers=headers)
            
            content_encoding = response.headers.get('content-encoding', '未设置')
            content_type = response.headers.get('content-type', '未设置')
            content_length = response.headers.get('content-length', '未设置')
            
            print(f"内容编码: {content_encoding}")
            print(f"内容类型: {content_type}")
            print(f"内容长度: {content_length}")
            
            # 分析压缩
            if 'br' in content_encoding:
                print("✅ Brotli压缩已启用")
                return True
            elif 'gzip' in content_encoding:
                print("✅ Gzip压缩已启用")
                return True
            elif content_type == 'image/webp':
                print("✅ WebP格式本身已压缩")
                return True
            else:
                print("⚪ 未检测到压缩（图片可能不需要）")
                return True
                
    except Exception as e:
        print(f"❌ 压缩检查失败: {e}")
        return False

async def main():
    """主函数"""
    print("🔍 验证Cloudflare缓存配置")
    print("=" * 50)
    
    print("📋 验证步骤:")
    print("1. 检查缓存规则是否生效")
    print("2. 测试性能提升效果")
    print("3. 验证压缩设置")
    
    # 运行验证
    tests = [
        ("缓存规则验证", verify_cache_rules),
        ("性能对比测试", performance_comparison),
        ("压缩设置检查", check_compression),
    ]
    
    results = []
    for test_name, test_func in tests:
        try:
            result = await test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name}异常: {e}")
            results.append((test_name, False))
    
    # 输出结果
    print("\n" + "=" * 50)
    print("📊 验证结果汇总")
    print("=" * 50)
    
    passed = 0
    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\n总体结果: {passed}/{len(results)} 项通过")
    
    if passed == len(results):
        print("\n🎉 配置验证成功！")
        print("\n📈 预期效果:")
        print("✅ 图片缓存在CDN边缘节点")
        print("✅ 后续访问速度显著提升")
        print("✅ 减少R2存储桶请求")
        print("✅ 降低流量成本")
    elif passed >= len(results) // 2:
        print("\n⚠️  部分配置生效，继续优化")
    else:
        print("\n❌ 配置可能有问题，请检查设置")
        print("\n🔧 检查清单:")
        print("1. 页面规则是否正确设置")
        print("2. URL模式是否匹配")
        print("3. 缓存级别是否设为'缓存所有内容'")

if __name__ == "__main__":
    asyncio.run(main())
