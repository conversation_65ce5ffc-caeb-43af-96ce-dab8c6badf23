from pydantic import BaseModel, EmailStr, Field, field_validator
from typing import Optional, List
from datetime import datetime
import re


class UserRegisterRequest(BaseModel):
    """用户注册请求模型"""

    username: str = Field(..., min_length=3, max_length=50, description="用户名")
    email: EmailStr = Field(..., description="邮箱地址")
    password: str = Field(..., min_length=8, max_length=128, description="密码")
    nickname: Optional[str] = Field(None, max_length=100, description="昵称")

    @field_validator("username")
    @classmethod
    def validate_username(cls, v):
        """验证用户名格式"""
        if not re.match(r"^[a-zA-Z0-9_-]+$", v):
            raise ValueError("用户名只能包含字母、数字、下划线和连字符")
        return v

    @field_validator("password")
    @classmethod
    def validate_password(cls, v):
        """验证密码强度"""
        if not re.search(r"[A-Z]", v):
            raise ValueError("密码必须包含至少一个大写字母")
        if not re.search(r"[a-z]", v):
            raise ValueError("密码必须包含至少一个小写字母")
        if not re.search(r"\d", v):
            raise ValueError("密码必须包含至少一个数字")
        return v


class UserLoginRequest(BaseModel):
    """用户登录请求模型"""

    username: str = Field(..., description="用户名或邮箱")
    password: str = Field(..., description="密码")
    remember_me: bool = Field(False, description="记住我")


class UserLoginResponse(BaseModel):
    """用户登录响应模型"""

    access_token: str = Field(..., description="访问令牌")
    refresh_token: str = Field(..., description="刷新令牌")
    token_type: str = Field("bearer", description="令牌类型")
    expires_in: int = Field(..., description="过期时间（秒）")
    user: "UserProfileResponse" = Field(..., description="用户信息")


class TokenRefreshRequest(BaseModel):
    """令牌刷新请求模型"""

    refresh_token: str = Field(..., description="刷新令牌")


class TokenRefreshResponse(BaseModel):
    """令牌刷新响应模型"""

    access_token: str = Field(..., description="新的访问令牌")
    token_type: str = Field("bearer", description="令牌类型")
    expires_in: int = Field(..., description="过期时间（秒）")


class ForgotPasswordRequest(BaseModel):
    """忘记密码请求模型"""

    email: EmailStr = Field(..., description="邮箱地址")


class ResetPasswordRequest(BaseModel):
    """重置密码请求模型"""

    token: str = Field(..., description="重置令牌")
    new_password: str = Field(..., min_length=8, max_length=128, description="新密码")

    @field_validator("new_password")
    @classmethod
    def validate_password(cls, v):
        """验证密码强度"""
        if not re.search(r"[A-Z]", v):
            raise ValueError("密码必须包含至少一个大写字母")
        if not re.search(r"[a-z]", v):
            raise ValueError("密码必须包含至少一个小写字母")
        if not re.search(r"\d", v):
            raise ValueError("密码必须包含至少一个数字")
        return v


class ChangePasswordRequest(BaseModel):
    """修改密码请求模型"""

    current_password: str = Field(..., description="当前密码")
    new_password: str = Field(..., min_length=8, max_length=128, description="新密码")

    @field_validator("new_password")
    @classmethod
    def validate_password(cls, v):
        """验证密码强度"""
        if not re.search(r"[A-Z]", v):
            raise ValueError("密码必须包含至少一个大写字母")
        if not re.search(r"[a-z]", v):
            raise ValueError("密码必须包含至少一个小写字母")
        if not re.search(r"\d", v):
            raise ValueError("密码必须包含至少一个数字")
        return v


class EmailVerificationRequest(BaseModel):
    """邮箱验证请求模型"""

    token: str = Field(..., description="验证令牌")


class ResendVerificationRequest(BaseModel):
    """重发验证邮件请求模型"""

    email: EmailStr = Field(..., description="邮箱地址")


class UserProfileResponse(BaseModel):
    """用户资料响应模型"""

    id: int
    username: str
    email: str
    nickname: Optional[str]
    avatar: Optional[str]
    phone: Optional[str]
    status: str
    email_verified: bool
    role: "RoleResponse"
    points: int = Field(default=0, description="用户积分")
    title: str = Field(default="资源拾荒者", description="用户头衔")
    last_login_at: Optional[datetime]
    created_at: datetime

    class Config:
        from_attributes = True


class UserProfileUpdateRequest(BaseModel):
    """用户资料更新请求模型"""

    nickname: Optional[str] = Field(None, max_length=100, description="昵称")
    phone: Optional[str] = Field(None, max_length=20, description="手机号")
    avatar: Optional[str] = Field(None, max_length=512, description="头像URL")


class RoleResponse(BaseModel):
    """角色响应模型"""

    id: int
    name: str
    display_name: str
    description: Optional[str]
    permissions: List[str]

    class Config:
        from_attributes = True


class UserListResponse(BaseModel):
    """用户列表响应模型"""

    id: int
    username: str
    email: str
    nickname: Optional[str]
    status: str
    email_verified: bool
    role: RoleResponse
    last_login_at: Optional[datetime]
    created_at: datetime

    class Config:
        from_attributes = True


class UserDetailResponse(BaseModel):
    """用户详情响应模型"""

    id: int
    username: str
    email: str
    nickname: Optional[str]
    avatar: Optional[str]
    phone: Optional[str]
    status: str
    email_verified: bool
    role: RoleResponse
    login_attempts: int
    locked_until: Optional[datetime]
    last_login_at: Optional[datetime]
    created_at: datetime
    updated_at: datetime

    class Config:
        from_attributes = True


class UserCreateRequest(BaseModel):
    """管理员创建用户请求模型"""

    username: str = Field(..., min_length=3, max_length=50, description="用户名")
    email: EmailStr = Field(..., description="邮箱地址")
    password: str = Field(..., min_length=8, max_length=128, description="密码")
    nickname: Optional[str] = Field(None, max_length=100, description="昵称")
    role_id: int = Field(..., description="角色ID")
    status: str = Field("active", description="用户状态")

    @field_validator("username")
    @classmethod
    def validate_username(cls, v):
        """验证用户名格式"""
        if not re.match(r"^[a-zA-Z0-9_-]+$", v):
            raise ValueError("用户名只能包含字母、数字、下划线和连字符")
        return v


class UserUpdateRequest(BaseModel):
    """管理员更新用户请求模型"""

    username: Optional[str] = Field(
        None, min_length=3, max_length=50, description="用户名"
    )
    email: Optional[EmailStr] = Field(None, description="邮箱地址")
    nickname: Optional[str] = Field(None, max_length=100, description="昵称")
    phone: Optional[str] = Field(None, max_length=20, description="手机号")
    role_id: Optional[int] = Field(None, description="角色ID")
    status: Optional[str] = Field(None, description="用户状态")

    @field_validator("username")
    @classmethod
    def validate_username(cls, v):
        """验证用户名格式"""
        if v and not re.match(r"^[a-zA-Z0-9_-]+$", v):
            raise ValueError("用户名只能包含字母、数字、下划线和连字符")
        return v


class UserStatusUpdateRequest(BaseModel):
    """用户状态更新请求模型"""

    status: str = Field(..., description="新状态")


class UserPasswordResetRequest(BaseModel):
    """管理员重置用户密码请求模型"""

    new_password: str = Field(..., min_length=8, max_length=128, description="新密码")

    @field_validator("new_password")
    @classmethod
    def validate_password(cls, v):
        """验证密码强度"""
        if not re.search(r"[A-Z]", v):
            raise ValueError("密码必须包含至少一个大写字母")
        if not re.search(r"[a-z]", v):
            raise ValueError("密码必须包含至少一个小写字母")
        if not re.search(r"\d", v):
            raise ValueError("密码必须包含至少一个数字")
        return v


class ApiResponse(BaseModel):
    """通用API响应模型"""

    status: str = Field("success", description="响应状态")
    message: str = Field("", description="响应消息")
    data: Optional[dict] = Field(None, description="响应数据")


# 更新前向引用
UserLoginResponse.model_rebuild()
UserProfileResponse.model_rebuild()
