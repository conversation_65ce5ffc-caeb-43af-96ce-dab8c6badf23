#!/usr/bin/env python3
"""
检查Cloudflare免费版CDN优化状态
"""

import asyncio
import httpx
from app.utils.config import settings

async def test_cache_behavior():
    """测试缓存行为"""
    print("🔄 测试缓存行为...")
    
    r2_config = settings.get("avatar.r2", {})
    custom_domain = r2_config.get("custom_domain")
    
    if not custom_domain:
        print("❌ 未配置自定义域名")
        return False
    
    test_url = f"{custom_domain}/test/custom_domain_test.webp"
    
    try:
        # 连续请求两次，检查缓存效果
        async with httpx.AsyncClient(timeout=30.0) as client:
            print(f"测试URL: {test_url}")
            
            # 第一次请求
            print("\n第一次请求:")
            response1 = await client.head(test_url)
            cache_status1 = response1.headers.get('cf-cache-status', 'UNKNOWN')
            print(f"  缓存状态: {cache_status1}")
            print(f"  状态码: {response1.status_code}")
            
            # 等待1秒后第二次请求
            await asyncio.sleep(1)
            
            print("\n第二次请求:")
            response2 = await client.head(test_url)
            cache_status2 = response2.headers.get('cf-cache-status', 'UNKNOWN')
            print(f"  缓存状态: {cache_status2}")
            print(f"  状态码: {response2.status_code}")
            
            # 分析缓存行为
            if cache_status2 == 'HIT':
                print("✅ 缓存工作正常 - 第二次请求命中缓存")
                return True
            elif cache_status1 == 'MISS' and cache_status2 == 'MISS':
                print("⚠️  缓存未生效 - 需要配置缓存规则")
                return False
            elif cache_status1 == 'DYNAMIC' and cache_status2 == 'DYNAMIC':
                print("⚠️  被识别为动态内容 - 需要设置缓存规则")
                return False
            else:
                print(f"⚪ 缓存状态: {cache_status1} -> {cache_status2}")
                return True
                
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False

async def check_free_optimizations():
    """检查免费版可用的优化"""
    print("\n🆓 检查免费版优化选项...")
    
    r2_config = settings.get("avatar.r2", {})
    custom_domain = r2_config.get("custom_domain")
    
    if not custom_domain:
        print("❌ 未配置自定义域名")
        return False
    
    test_url = f"{custom_domain}/test/custom_domain_test.webp"
    
    try:
        async with httpx.AsyncClient(timeout=30.0) as client:
            response = await client.head(test_url)
            
            # 检查免费版可用的优化
            optimizations = {
                'server': {
                    'header': 'server',
                    'expected': 'cloudflare',
                    'description': 'Cloudflare代理',
                    'status': '✅' if response.headers.get('server') == 'cloudflare' else '❌'
                },
                'cf-ray': {
                    'header': 'cf-ray',
                    'expected': 'exists',
                    'description': 'CDN处理',
                    'status': '✅' if response.headers.get('cf-ray') else '❌'
                },
                'content-encoding': {
                    'header': 'content-encoding',
                    'expected': 'gzip',
                    'description': 'Gzip压缩',
                    'status': '✅' if 'gzip' in response.headers.get('content-encoding', '') else '⚪'
                },
                'strict-transport-security': {
                    'header': 'strict-transport-security',
                    'expected': 'exists',
                    'description': 'HTTPS强制',
                    'status': '✅' if response.headers.get('strict-transport-security') else '⚪'
                }
            }
            
            print("📋 免费版优化状态:")
            for key, opt in optimizations.items():
                value = response.headers.get(opt['header'], '未设置')
                print(f"  {opt['status']} {opt['description']}: {value}")
            
            return True
            
    except Exception as e:
        print(f"❌ 检查失败: {e}")
        return False

async def suggest_free_optimizations():
    """建议免费版优化方案"""
    print("\n💡 免费版优化建议:")
    
    print("\n1. 📋 缓存规则设置 (免费):")
    print("   在Cloudflare控制台 → 缓存 → 配置:")
    print("   - 缓存级别: 标准")
    print("   - 浏览器缓存TTL: 4小时")
    print("   - 始终在线: 启用")
    
    print("\n2. 🔧 页面规则设置 (免费版限3条):")
    print("   规则1: avatars.pansoo.cn/avatars/*")
    print("   设置: 缓存级别 = 缓存所有内容")
    print("   设置: 边缘缓存TTL = 1个月")
    
    print("\n3. ⚡ 速度优化 (免费):")
    print("   - 自动缩小: 启用 CSS/JS/HTML")
    print("   - Brotli压缩: 启用")
    print("   - HTTP/2: 启用")
    print("   - 0-RTT: 启用")
    
    print("\n4. 🛡️ 安全设置 (免费):")
    print("   - 始终使用HTTPS: 启用")
    print("   - HSTS: 启用")
    print("   - 安全级别: 中等")
    
    print("\n5. 📊 代码层面优化 (已实现):")
    print("   ✅ WebP格式转换")
    print("   ✅ 图片压缩 (质量80%)")
    print("   ✅ 统一尺寸 (400x400)")
    print("   ✅ 旧文件清理")
    
    return True

async def test_actual_performance():
    """测试实际性能"""
    print("\n⚡ 实际性能测试...")
    
    r2_config = settings.get("avatar.r2", {})
    custom_domain = r2_config.get("custom_domain")
    
    if not custom_domain:
        print("❌ 未配置自定义域名")
        return False
    
    test_url = f"{custom_domain}/test/custom_domain_test.webp"
    
    try:
        import time
        times = []
        
        # 测试5次请求
        async with httpx.AsyncClient(timeout=30.0) as client:
            for i in range(5):
                start_time = time.time()
                response = await client.head(test_url)
                end_time = time.time()
                
                response_time = (end_time - start_time) * 1000
                times.append(response_time)
                
                cache_status = response.headers.get('cf-cache-status', 'UNKNOWN')
                print(f"  请求 {i+1}: {response_time:.2f}ms (缓存: {cache_status})")
                
                await asyncio.sleep(0.5)  # 间隔0.5秒
        
        # 计算统计
        avg_time = sum(times) / len(times)
        min_time = min(times)
        max_time = max(times)
        
        print(f"\n📊 性能统计:")
        print(f"  平均响应时间: {avg_time:.2f}ms")
        print(f"  最快响应时间: {min_time:.2f}ms")
        print(f"  最慢响应时间: {max_time:.2f}ms")
        
        # 性能评估
        if avg_time < 500:
            print("✅ 性能优秀 (<500ms)")
        elif avg_time < 1000:
            print("✅ 性能良好 (<1s)")
        elif avg_time < 2000:
            print("⚠️  性能一般 (<2s)")
        else:
            print("❌ 性能较差 (>2s)")
        
        return True
        
    except Exception as e:
        print(f"❌ 性能测试失败: {e}")
        return False

async def main():
    """主函数"""
    print("🆓 Cloudflare免费版CDN优化检查")
    print("=" * 50)
    
    # 显示配置
    r2_config = settings.get("avatar.r2", {})
    print("📋 当前配置:")
    print(f"  自定义域名: {r2_config.get('custom_domain')}")
    print(f"  存储桶: {r2_config.get('bucket_name')}")
    
    # 运行检查
    tests = [
        ("缓存行为测试", test_cache_behavior),
        ("免费版优化检查", check_free_optimizations),
        ("性能测试", test_actual_performance),
        ("优化建议", suggest_free_optimizations),
    ]
    
    results = []
    for test_name, test_func in tests:
        try:
            result = await test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name}异常: {e}")
            results.append((test_name, False))
    
    # 输出结果
    print("\n" + "=" * 50)
    print("📊 免费版CDN状态汇总")
    print("=" * 50)
    
    passed = 0
    for test_name, result in results:
        if test_name != "优化建议":  # 建议不算测试结果
            status = "✅ 正常" if result else "❌ 需优化"
            print(f"{test_name}: {status}")
            if result:
                passed += 1
    
    print(f"\n总体状态: {passed}/{len(results)-1} 项正常")
    
    print("\n🎯 免费版优化重点:")
    print("1. 设置页面规则强制缓存图片")
    print("2. 启用基础压缩和优化")
    print("3. 代码层面已经做了最佳优化")
    print("4. 监控缓存命中率")

if __name__ == "__main__":
    asyncio.run(main())
