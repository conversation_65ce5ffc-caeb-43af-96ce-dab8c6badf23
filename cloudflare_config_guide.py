#!/usr/bin/env python3
"""
Cloudflare配置指导
"""

def print_config_guide():
    """打印详细的配置指导"""
    
    print("🚀 Cloudflare免费版CDN优化配置指导")
    print("=" * 60)
    
    print("\n📋 配置步骤（按优先级排序）:")
    
    print("\n🎯 第一步：设置页面规则（最重要）")
    print("-" * 40)
    print("1. 登录 https://dash.cloudflare.com")
    print("2. 选择您的域名 pansoo.cn")
    print("3. 点击左侧菜单 '规则' → '页面规则'")
    print("4. 点击 '创建页面规则'")
    print("5. 配置如下:")
    print()
    print("   URL模式: avatars.pansoo.cn/*")
    print("   设置:")
    print("     ✅ 缓存级别: 缓存所有内容")
    print("     ✅ 边缘缓存TTL: 1个月")
    print("     ✅ 浏览器缓存TTL: 1天")
    print("     ✅ 安全级别: 基本上关闭")
    print()
    print("6. 点击 '保存并部署'")
    
    print("\n⚡ 第二步：启用速度优化")
    print("-" * 40)
    print("1. 在同一域名下，点击 '速度' → '优化'")
    print("2. 启用以下选项:")
    print("     ✅ 自动缩小 - HTML")
    print("     ✅ 自动缩小 - CSS")
    print("     ✅ 自动缩小 - JavaScript")
    print("     ✅ Brotli")
    print("     ✅ 早期提示")
    
    print("\n🔒 第三步：安全设置")
    print("-" * 40)
    print("1. 点击 'SSL/TLS' → '边缘证书'")
    print("2. 启用以下选项:")
    print("     ✅ 始终使用HTTPS")
    print("     ✅ HTTP严格传输安全(HSTS)")
    print("     ✅ 最小TLS版本: 1.2")
    
    print("\n🌐 第四步：网络设置")
    print("-" * 40)
    print("1. 点击 '网络'")
    print("2. 启用以下选项:")
    print("     ✅ HTTP/2")
    print("     ✅ 0-RTT连接恢复")
    print("     ✅ IPv6兼容性")
    print("     ✅ WebSockets")
    
    print("\n📊 第五步：缓存配置")
    print("-" * 40)
    print("1. 点击 '缓存' → '配置'")
    print("2. 设置:")
    print("     ✅ 缓存级别: 标准")
    print("     ✅ 始终在线: 启用")
    print("     ✅ 开发模式: 关闭")
    
    print("\n⏰ 配置生效时间")
    print("-" * 40)
    print("📌 页面规则: 立即生效")
    print("📌 缓存设置: 5-10分钟")
    print("📌 SSL设置: 15-30分钟")
    print("📌 DNS更改: 最多24小时")
    
    print("\n🔍 验证配置")
    print("-" * 40)
    print("配置完成后，运行以下命令验证:")
    print("python verify_cache_config.py")
    print()
    print("预期结果:")
    print("✅ 缓存状态: MISS → HIT")
    print("✅ 响应时间: <500ms")
    print("✅ 缓存控制头: 已设置")

def print_troubleshooting():
    """打印故障排除指南"""
    
    print("\n🔧 故障排除指南")
    print("=" * 60)
    
    print("\n❓ 问题1: 缓存状态仍显示DYNAMIC")
    print("解决方案:")
    print("1. 检查页面规则URL模式是否正确")
    print("2. 确认缓存级别设为'缓存所有内容'")
    print("3. 等待10分钟后重新测试")
    print("4. 尝试不同的URL模式:")
    print("   - avatars.pansoo.cn/*")
    print("   - *.pansoo.cn/test/*")
    print("   - *.pansoo.cn/*.webp")
    
    print("\n❓ 问题2: 响应时间仍然很慢")
    print("解决方案:")
    print("1. 确认页面规则已生效")
    print("2. 检查是否启用了Brotli压缩")
    print("3. 验证HTTP/2是否启用")
    print("4. 清除浏览器缓存重新测试")
    
    print("\n❓ 问题3: 404错误")
    print("解决方案:")
    print("1. 确认文件已成功上传到R2")
    print("2. 检查自定义域名DNS设置")
    print("3. 验证R2存储桶公共访问设置")
    
    print("\n❓ 问题4: SSL证书错误")
    print("解决方案:")
    print("1. 等待SSL证书自动颁发(最多30分钟)")
    print("2. 检查DNS设置是否正确")
    print("3. 确认域名已添加到Cloudflare")

def print_monitoring_tips():
    """打印监控建议"""
    
    print("\n📊 监控和优化建议")
    print("=" * 60)
    
    print("\n📈 关键指标监控:")
    print("1. 缓存命中率 (目标: >90%)")
    print("2. 平均响应时间 (目标: <500ms)")
    print("3. R2请求次数 (应显著减少)")
    print("4. 流量成本 (应有所降低)")
    
    print("\n🔍 Cloudflare Analytics:")
    print("1. 访问 Cloudflare控制台 → Analytics")
    print("2. 查看 '缓存' 标签页")
    print("3. 监控缓存命中率趋势")
    print("4. 分析热门请求路径")
    
    print("\n⚙️ 持续优化:")
    print("1. 根据Analytics调整缓存TTL")
    print("2. 优化热门文件的缓存策略")
    print("3. 考虑升级到Pro版(如果流量很大)")
    print("4. 定期检查配置是否最优")
    
    print("\n💰 成本优化:")
    print("1. 监控R2请求次数")
    print("2. 设置合理的缓存TTL")
    print("3. 清理不需要的旧文件")
    print("4. 考虑图片进一步压缩")

def main():
    """主函数"""
    print_config_guide()
    print_troubleshooting()
    print_monitoring_tips()
    
    print("\n🎯 总结")
    print("=" * 60)
    print("✅ 最重要: 设置页面规则强制缓存")
    print("✅ 次重要: 启用基础压缩优化")
    print("✅ 监控: 定期检查缓存命中率")
    print("✅ 优化: 根据数据持续改进")
    
    print("\n📞 需要帮助?")
    print("配置完成后运行测试脚本验证效果")
    print("如有问题，可以分享测试结果进行诊断")

if __name__ == "__main__":
    main()
