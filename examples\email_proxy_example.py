#!/usr/bin/env python3
"""
邮件服务代理使用示例
演示如何配置和使用代理发送Gmail邮件
"""

import asyncio
import sys
import os
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from app.core.email import EmailService
from app.utils.config import settings


async def example_basic_email():
    """基础邮件发送示例"""
    print("=== 基础邮件发送示例 ===")
    
    email_service = EmailService()
    
    if not email_service.enabled:
        print("❌ 邮件服务未启用，请检查配置")
        return
    
    try:
        await email_service._send_email(
            to_email="<EMAIL>",
            subject="测试邮件",
            html_content="""
            <h1>这是一封测试邮件</h1>
            <p>如果您收到这封邮件，说明邮件服务配置正确。</p>
            <p>发送时间: {}</p>
            """.format(asyncio.get_event_loop().time())
        )
        print("✅ 邮件发送成功")
    except Exception as e:
        print(f"❌ 邮件发送失败: {e}")


async def example_verification_email():
    """验证邮件发送示例"""
    print("\n=== 验证邮件发送示例 ===")
    
    email_service = EmailService()
    
    if not email_service.enabled:
        print("❌ 邮件服务未启用，请检查配置")
        return
    
    try:
        await email_service.send_verification_email(
            to_email="<EMAIL>",
            username="测试用户",
            verify_token="example-token-123456"
        )
        print("✅ 验证邮件发送成功")
    except Exception as e:
        print(f"❌ 验证邮件发送失败: {e}")


async def example_password_reset_email():
    """密码重置邮件发送示例"""
    print("\n=== 密码重置邮件发送示例 ===")
    
    email_service = EmailService()
    
    if not email_service.enabled:
        print("❌ 邮件服务未启用，请检查配置")
        return
    
    try:
        await email_service.send_password_reset_email(
            to_email="<EMAIL>",
            username="测试用户",
            reset_token="reset-token-123456"
        )
        print("✅ 密码重置邮件发送成功")
    except Exception as e:
        print(f"❌ 密码重置邮件发送失败: {e}")


def show_current_config():
    """显示当前邮件配置"""
    print("=== 当前邮件配置 ===")
    
    email_service = EmailService()
    
    print(f"SMTP服务器: {email_service.smtp_server}")
    print(f"SMTP端口: {email_service.smtp_port}")
    print(f"用户名: {email_service.username}")
    print(f"发件人: {email_service.from_name} <{email_service.from_email}>")
    print(f"服务状态: {'启用' if email_service.enabled else '禁用'}")
    
    if email_service.proxy_enabled:
        print(f"代理状态: 启用")
        print(f"代理类型: {email_service.proxy_type}")
        print(f"代理地址: {email_service.proxy_host}:{email_service.proxy_port}")
        if email_service.proxy_username:
            print(f"代理认证: 是")
        else:
            print(f"代理认证: 否")
    else:
        print(f"代理状态: 禁用")


async def main():
    """主函数"""
    print("Pan-So 邮件服务代理使用示例")
    print("=" * 50)
    
    # 显示当前配置
    show_current_config()
    
    # 检查是否在调试模式
    if settings.get("app.debug", True):
        print("\n⚠️  当前处于调试模式，邮件不会实际发送，只会记录日志")
    
    print("\n请选择要运行的示例:")
    print("1. 基础邮件发送")
    print("2. 验证邮件发送")
    print("3. 密码重置邮件发送")
    print("4. 运行所有示例")
    print("0. 退出")
    
    choice = input("\n请输入选择 (0-4): ").strip()
    
    if choice == "1":
        await example_basic_email()
    elif choice == "2":
        await example_verification_email()
    elif choice == "3":
        await example_password_reset_email()
    elif choice == "4":
        await example_basic_email()
        await example_verification_email()
        await example_password_reset_email()
    elif choice == "0":
        print("退出示例程序")
    else:
        print("无效选择")


if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\n程序被用户中断")
    except Exception as e:
        print(f"\n程序运行出错: {e}")
        import traceback
        traceback.print_exc()
