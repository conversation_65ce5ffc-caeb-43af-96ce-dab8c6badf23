"""
个人信息管理API接口
"""

from fastapi import APIRouter, HTTPException, status, Depends, UploadFile, File, Request
from typing import Optional
import logging

from app.models.user import User
from app.models.profile_models import (
    UserProfileDetailResponse,
    UserProfileUpdateRequest,
    ChangeEmailRequest,
    VerifyEmailChangeRequest,
    ChangePasswordRequest,
    ChangeNicknameRequest,
    AvatarUploadResponse,
)
from app.models.auth_models import ApiResponse
from app.core.permissions import get_current_user
from app.services.profile_service import ProfileService
from app.services.avatar_service import AvatarService
from app.services.nickname_service import NicknameService
from app.services.email_change_service import EmailChangeService

logger = logging.getLogger(__name__)

router = APIRouter(prefix="/api/profile", tags=["个人信息管理"])


@router.get("/me", response_model=ApiResponse)
async def get_my_profile(current_user: User = Depends(get_current_user)):
    """获取当前用户完整个人信息"""
    try:
        profile_data = await ProfileService.get_user_profile_detail(current_user)

        return ApiResponse(
            status="success", message="获取个人信息成功", data=profile_data
        )
    except Exception as e:
        logger.error(f"获取用户个人信息失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail="获取个人信息失败"
        )


@router.put("/me", response_model=ApiResponse)
async def update_my_profile(
    profile_data: UserProfileUpdateRequest,
    current_user: User = Depends(get_current_user),
    request: Request = None,
):
    """批量更新用户基础信息"""
    try:
        updated_user = await ProfileService.update_user_profile(
            user=current_user,
            profile_data=profile_data,
            ip_address=request.client.host if request else None,
            user_agent=request.headers.get("user-agent") if request else None,
        )

        return ApiResponse(
            status="success",
            message="个人信息更新成功",
            data={
                "id": updated_user.id,
                "nickname": updated_user.nickname,
                "updated_at": updated_user.updated_at,
            },
        )
    except ValueError as e:
        raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail=str(e))
    except Exception as e:
        logger.error(f"更新用户个人信息失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail="更新个人信息失败"
        )


@router.post("/change-email", response_model=ApiResponse)
async def request_email_change(
    email_data: ChangeEmailRequest,
    current_user: User = Depends(get_current_user),
    request: Request = None,
):
    """请求更改邮箱（发送验证邮件）"""
    try:
        change_request = await EmailChangeService.request_email_change(
            user=current_user,
            new_email=email_data.new_email,
            password=email_data.password,
            ip_address=request.client.host if request else None,
        )

        return ApiResponse(
            status="success",
            message="邮箱更改验证邮件已发送，请查收邮件并点击验证链接",
            data={
                "request_id": change_request.id,
                "new_email": change_request.new_email,
                "expires_at": change_request.token_expires_at,
            },
        )
    except ValueError as e:
        raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail=str(e))
    except Exception as e:
        logger.error(f"请求邮箱更改失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail="请求邮箱更改失败"
        )


@router.post("/verify-email-change", response_model=ApiResponse)
async def verify_email_change(
    verify_data: VerifyEmailChangeRequest, request: Request = None
):
    """验证邮箱更改"""
    try:
        user = await EmailChangeService.verify_email_change(
            token=verify_data.token, ip_address=request.client.host if request else None
        )

        return ApiResponse(
            status="success",
            message="邮箱更改成功",
            data={
                "user_id": user.id,
                "new_email": user.email,
                "verified_at": user.updated_at,
            },
        )
    except ValueError as e:
        raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail=str(e))
    except Exception as e:
        logger.error(f"验证邮箱更改失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail="验证邮箱更改失败"
        )


@router.post("/upload-avatar", response_model=ApiResponse)
async def upload_avatar(
    file: UploadFile = File(...), current_user: User = Depends(get_current_user)
):
    """
    上传用户头像

    - 支持格式：JPG, PNG, GIF, WebP
    - 自动转换为WebP格式，质量80%
    - 自动调整为400x400尺寸
    - 存储到Cloudflare R2，全球CDN加速
    - 上传新头像时会自动清理旧头像文件
    """
    try:
        avatar_data = await AvatarService.upload_avatar(user=current_user, file=file)

        return ApiResponse(
            status="success", message="头像上传成功，已转换为WebP格式", data=avatar_data
        )
    except ValueError as e:
        # 文件验证错误（格式、大小等）
        raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail=str(e))
    except Exception as e:
        logger.error(f"头像上传失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="头像上传失败，请稍后重试",
        )


@router.post("/change-password", response_model=ApiResponse)
async def change_password(
    password_data: ChangePasswordRequest, current_user: User = Depends(get_current_user)
):
    """修改密码"""
    try:
        await ProfileService.change_password(
            user=current_user,
            old_password=password_data.old_password,
            new_password=password_data.new_password,
        )

        return ApiResponse(
            status="success",
            message="密码修改成功，请重新登录",
            data={"changed_at": current_user.updated_at},
        )
    except ValueError as e:
        raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail=str(e))
    except Exception as e:
        logger.error(f"修改密码失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail="修改密码失败"
        )


@router.post("/change-nickname", response_model=ApiResponse)
async def change_nickname(
    nickname_data: ChangeNicknameRequest,
    current_user: User = Depends(get_current_user),
    request: Request = None,
):
    """修改昵称"""
    try:
        updated_user = await NicknameService.change_nickname(
            user=current_user,
            new_nickname=nickname_data.nickname,
            reason=nickname_data.reason,
            ip_address=request.client.host if request else None,
            user_agent=request.headers.get("user-agent") if request else None,
        )

        return ApiResponse(
            status="success",
            message="昵称修改成功",
            data={
                "user_id": updated_user.id,
                "old_nickname": current_user.nickname,
                "new_nickname": updated_user.nickname,
                "next_change_date": updated_user.get_next_nickname_change_date(),
                "changed_at": updated_user.updated_at,
            },
        )
    except ValueError as e:
        raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail=str(e))
    except Exception as e:
        logger.error(f"修改昵称失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail="修改昵称失败"
        )


@router.get("/points-history", response_model=ApiResponse)
async def get_points_history(
    page: int = 1, size: int = 20, current_user: User = Depends(get_current_user)
):
    """获取用户积分获取日志"""
    try:
        from app.services.points_service import PointsService

        history_data = await PointsService.get_user_points_history(
            user=current_user, page=page, size=size
        )

        return ApiResponse(
            status="success", message="获取积分历史成功", data=history_data
        )
    except Exception as e:
        logger.error(f"获取积分历史失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail="获取积分历史失败"
        )


@router.get("/help-requests", response_model=ApiResponse)
async def get_my_help_requests(
    page: int = 1,
    size: int = 20,
    status_filter: Optional[str] = None,
    current_user: User = Depends(get_current_user),
):
    """获取我的求助列表"""
    try:
        from app.services.help_request_service import HelpRequestService

        requests_data = await HelpRequestService.get_user_help_requests(
            user=current_user, page=page, size=size, status_filter=status_filter
        )

        return ApiResponse(
            status="success", message="获取求助列表成功", data=requests_data
        )
    except Exception as e:
        logger.error(f"获取求助列表失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail="获取求助列表失败"
        )


@router.get("/help-answers", response_model=ApiResponse)
async def get_my_help_answers(
    page: int = 1, size: int = 20, current_user: User = Depends(get_current_user)
):
    """获取我的回答列表"""
    try:
        from app.services.help_request_service import HelpRequestService

        answers_data = await HelpRequestService.get_user_help_answers(
            user=current_user, page=page, size=size
        )

        return ApiResponse(
            status="success", message="获取回答列表成功", data=answers_data
        )
    except Exception as e:
        logger.error(f"获取回答列表失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail="获取回答列表失败"
        )


@router.get("/statistics", response_model=ApiResponse)
async def get_profile_statistics(current_user: User = Depends(get_current_user)):
    """获取用户统计信息"""
    try:
        stats_data = await ProfileService.get_user_statistics(current_user)

        return ApiResponse(
            status="success", message="获取统计信息成功", data=stats_data
        )
    except Exception as e:
        logger.error(f"获取统计信息失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail="获取统计信息失败"
        )


@router.get("/activity-summary", response_model=ApiResponse)
async def get_activity_summary(current_user: User = Depends(get_current_user)):
    """获取用户活动摘要"""
    try:
        activity_data = await ProfileService.get_user_activity_summary(current_user)

        return ApiResponse(
            status="success", message="获取活动摘要成功", data=activity_data
        )
    except Exception as e:
        logger.error(f"获取活动摘要失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail="获取活动摘要失败"
        )
